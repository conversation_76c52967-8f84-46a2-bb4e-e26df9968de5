/**
 * 主题 API 路由
 *
 * 功能特性：
 * - 动态主题文件服务
 * - 缓存控制
 * - 压缩支持
 * - 性能监控
 * - 错误处理
 */
import { promises as fs } from 'fs';
import path from 'path';

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// 主题请求参数验证
const ThemeParamsSchema = z.object({
  theme: z
    .string()
    .min(1)
    .max(50)
    .regex(/^[\w-]+$/),
});

// 查询参数验证
const QueryParamsSchema = z.object({
  compress: z
    .string()
    .optional()
    .transform((val) => val === 'true'),
  format: z.enum(['json', 'css', 'scss']).optional().default('json'),
  version: z.string().optional(),
});

// 主题文件映射
const THEME_FILES: Record<string, string> = {
  light: 'light.json',
  dark: 'dark.json',
  system: 'system.json',
  'high-contrast': 'high-contrast.json',
  blue: 'blue.json',
  green: 'green.json',
  purple: 'purple.json',
  red: 'red.json',
  orange: 'orange.json',
  yellow: 'yellow.json',
};

// 主题数据类型定义
interface ThemeColors {
  background: string;
  foreground: string;
  primary: string;
  secondary: string;
  accent: string;
  muted: string;
  border: string;
  input: string;
  ring: string;
  destructive: string;
  'destructive-foreground': string;
  'primary-foreground': string;
  'secondary-foreground': string;
  'accent-foreground': string;
  'muted-foreground': string;
}

interface ThemeData {
  name: string;
  type: 'light' | 'dark';
  colors: ThemeColors;
  id?: string;
  fallback?: boolean;
}

// 默认主题数据
const DEFAULT_THEMES: Record<string, ThemeData> = {
  light: {
    name: 'Light',
    type: 'light',
    colors: {
      background: '#ffffff',
      foreground: '#000000',
      primary: '#0066cc',
      secondary: '#6b7280',
      accent: '#3b82f6',
      muted: '#f3f4f6',
      border: '#e5e7eb',
      input: '#ffffff',
      ring: '#3b82f6',
      destructive: '#ef4444',
      'destructive-foreground': '#ffffff',
      'primary-foreground': '#ffffff',
      'secondary-foreground': '#ffffff',
      'accent-foreground': '#ffffff',
      'muted-foreground': '#6b7280',
    },
  },
  dark: {
    name: 'Dark',
    type: 'dark',
    colors: {
      background: '#000000',
      foreground: '#ffffff',
      primary: '#3b82f6',
      secondary: '#6b7280',
      accent: '#60a5fa',
      muted: '#1f2937',
      border: '#374151',
      input: '#111827',
      ring: '#3b82f6',
      destructive: '#ef4444',
      'destructive-foreground': '#ffffff',
      'primary-foreground': '#ffffff',
      'secondary-foreground': '#ffffff',
      'accent-foreground': '#ffffff',
      'muted-foreground': '#9ca3af',
    },
  },
  'high-contrast': {
    name: 'High Contrast',
    type: 'light',
    colors: {
      background: '#ffffff',
      foreground: '#000000',
      primary: '#0000ff',
      secondary: '#666666',
      accent: '#ff0000',
      muted: '#f0f0f0',
      border: '#000000',
      input: '#ffffff',
      ring: '#0000ff',
      destructive: '#ff0000',
      'destructive-foreground': '#ffffff',
      'primary-foreground': '#ffffff',
      'secondary-foreground': '#ffffff',
      'accent-foreground': '#ffffff',
      'muted-foreground': '#333333',
    },
  },
};

// 创建主题映射以避免对象注入
const DEFAULT_THEMES_MAP = new Map(Object.entries(DEFAULT_THEMES));

/**
 * 获取默认主题数据
 */
function getDefaultTheme(themeName: string): ThemeData | null {
  const theme = DEFAULT_THEMES_MAP.get(themeName);
  return theme ?? null;
}

// 创建文件映射以避免对象注入
const THEME_FILES_MAP = new Map(Object.entries(THEME_FILES));

/**
 * 获取主题文件名
 */
function getThemeFileName(themeName: string): string | null {
  const fileName = THEME_FILES_MAP.get(themeName);
  return fileName ?? null;
}

/**
 * 格式化主题数据
 */
function formatThemeData(
  themeData: ThemeData,
  format: string
): ThemeData | string {
  if (format === 'css') {
    return convertToCss(themeData);
  } else if (format === 'scss') {
    return convertToScss(themeData);
  }
  return themeData;
}

/**
 * 验证文件安全性
 */
function validateFileSecurity(fileName: string, themesDir: string): string {
  // 安全的文件路径构建，防止路径遍历攻击
  const safePath = path.resolve(themesDir, fileName);
  if (!safePath.startsWith(themesDir)) {
    throw new Error('Invalid file path');
  }

  // 使用白名单验证文件名
  const allowedFiles = Object.values(THEME_FILES);
  if (!allowedFiles.includes(fileName)) {
    throw new Error('File not in whitelist');
  }

  return safePath;
}

/**
 * 从文件系统加载主题
 */
async function loadThemeFromFile(
  themeName: string,
  format: string
): Promise<ThemeData | string> {
  const fileName = getThemeFileName(themeName);
  if (fileName === null) {
    throw new Error(`Theme '${themeName}' not found`);
  }

  const themesDir = path.join(process.cwd(), 'src/styles/themes');
  const safePath = validateFileSecurity(fileName, themesDir);

  // ESLint 安全规则要求：使用字面量路径读取文件
  const fileContent = await fs.readFile(safePath, 'utf-8');
  const themeData = JSON.parse(fileContent) as ThemeData;

  return formatThemeData(themeData, format);
}

/**
 * 获取主题数据
 */
async function getThemeData(
  themeName: string,
  format: string
): Promise<ThemeData | string | null> {
  // 首先检查默认主题
  const themeData = getDefaultTheme(themeName);
  if (themeData !== null) {
    return formatThemeData(themeData, format);
  }

  // 尝试从文件系统加载
  try {
    return await loadThemeFromFile(themeName, format);
  } catch (error) {
    // 如果文件不存在，返回基础主题
    if (process.env.NODE_ENV === 'development') {
       
      if (process.env.NODE_ENV === 'development') {
        console.warn(
        `Theme file not found for '${themeName}', using fallback:`,
        error
      );
      }
    }
    return getFallbackTheme(themeName);
  }
}

/**
 * 获取回退主题
 */
function getFallbackTheme(themeName: string): ThemeData {
  const baseTheme = themeName.includes('dark')
    ? DEFAULT_THEMES['dark']
    : DEFAULT_THEMES['light'];

  if (baseTheme === undefined) {
    throw new Error(`Base theme not found for '${themeName}'`);
  }

  return {
    ...baseTheme,
    name: themeName.charAt(0).toUpperCase() + themeName.slice(1),
    id: themeName,
    fallback: true,
  };
}

/**
 * 转换为 CSS 格式
 */
function convertToCss(themeData: ThemeData): string {
  const { colors } = themeData;

  let css = `:root {\n`;

  for (const [key, value] of Object.entries(colors)) {
    const cssVar = `--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
    css += `  ${cssVar}: ${value};\n`;
  }

  css += `}\n`;

  return css;
}

/**
 * 转换为 SCSS 格式
 */
function convertToScss(themeData: ThemeData): string {
  const { colors } = themeData;

  let scss = `// Theme: ${themeData.name}\n`;

  for (const [key, value] of Object.entries(colors)) {
    const variableName = `$${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`;
    scss += `${variableName}: ${value};\n`;
  }

  return scss;
}

/**
 * 设置缓存头
 */
function setCacheHeaders(response: NextResponse, maxAge = 3600): void {
  response.headers.set(
    'Cache-Control',
    `public, max-age=${maxAge}, s-maxage=${maxAge}`
  );
  response.headers.set('ETag', `"${Date.now()}"`);
  response.headers.set('Vary', 'Accept-Encoding');
}

/**
 * 解析请求参数
 */
async function parseRequestParams(
  request: NextRequest,
  params: Promise<{ theme: string }>
): Promise<{
  theme: string;
  compress: boolean;
  format: string;
  version: string | null;
}> {
  const resolvedParams = await params;
  const { theme } = ThemeParamsSchema.parse(resolvedParams);
  const { searchParams } = new URL(request.url);
  const { compress, format, version } = QueryParamsSchema.parse({
    compress: searchParams.get('compress'),
    format: searchParams.get('format'),
    version: searchParams.get('version'),
  });

  return { theme, compress, format, version: version ?? null };
}

/**
 * 创建响应数据
 */
function createResponseData(
  themeData: ThemeData | string,
  theme: string,
  format: string,
  version: string | null,
  compress: boolean,
  loadTime: number
): unknown {
  return {
    ...(typeof themeData === 'string' ? { content: themeData } : themeData),
    metadata: {
      theme,
      format,
      version: version ?? '1.0.0',
      timestamp: new Date().toISOString(),
      loadTime,
      compressed: compress,
    },
  };
}

/**
 * 创建主题响应
 */
function createThemeResponse(
  themeData: ThemeData | string,
  format: string,
  responseData: unknown,
  compress: boolean
): NextResponse {
  if (format === 'css' || format === 'scss') {
    return new NextResponse(String(themeData), {
      headers: {
        'Content-Type': format === 'css' ? 'text/css' : 'text/scss',
      },
    });
  }

  const content = compress
    ? JSON.stringify(responseData)
    : JSON.stringify(responseData, null, 2);

  return new NextResponse(content, {
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * GET 请求处理器
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ theme: string }> }
): Promise<NextResponse> {
  const startTime = Date.now();

  try {
    const { theme, compress, format, version } = await parseRequestParams(
      request,
      params
    );

    const themeData = await getThemeData(theme, format);
    if (themeData === null) {
      return NextResponse.json({ error: 'Theme not found' }, { status: 404 });
    }

    const responseData = createResponseData(
      themeData,
      theme,
      format,
      version,
      compress,
      Date.now() - startTime
    );

    const response = createThemeResponse(
      themeData,
      format,
      responseData,
      compress
    );

    setCacheHeaders(response, 3600);
    response.headers.set('X-Load-Time', `${Date.now() - startTime}ms`);
    response.headers.set('X-Theme-Name', theme);
    response.headers.set('X-Format', format);

    return response;
  } catch (error) {
    return createErrorResponse(error, startTime);
  }
}

/**
 * 创建错误响应
 */
function createErrorResponse(error: unknown, startTime: number): NextResponse {
  if (process.env.NODE_ENV === 'development') {
     
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
        console.error('Theme API error:', error);
      }
    }
  }

  const errorResponse = {
    error: 'Theme not found',
    message: error instanceof Error ? error.message : 'Unknown error',
    theme: 'unknown',
    timestamp: new Date().toISOString(),
    loadTime: Date.now() - startTime,
  };

  return new NextResponse(JSON.stringify(errorResponse), {
    status: 404,
    headers: {
      'Content-Type': 'application/json',
      'X-Load-Time': `${Date.now() - startTime}ms`,
    },
  });
}

/**
 * HEAD 请求处理器（用于缓存检查）
 */
export async function HEAD(
  request: NextRequest,
  { params }: { params: Promise<{ theme: string }> }
): Promise<NextResponse> {
  try {
    const resolvedParams = await params;
    const { theme } = ThemeParamsSchema.parse(resolvedParams);

    // 检查主题是否存在
    const exists = theme in DEFAULT_THEMES || theme in THEME_FILES;

    if (!exists) {
      return new NextResponse(null, { status: 404 });
    }

    const response = new NextResponse(null, { status: 200 });
    setCacheHeaders(response);
    response.headers.set('X-Theme-Exists', 'true');

    return response;
  } catch {
    return new NextResponse(null, { status: 404 });
  }
}

/**
 * OPTIONS 请求处理器（CORS 支持）
 */
export function OPTIONS(): NextResponse {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Accept, Accept-Encoding',
      'Access-Control-Max-Age': '86400',
    },
  });
}
