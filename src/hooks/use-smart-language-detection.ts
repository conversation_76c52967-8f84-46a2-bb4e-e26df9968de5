/**
 * 智能语言检测 React Hook
 *
 * 功能特性：
 * - 自动检测用户语言偏好
 * - 智能语言切换和持久化
 * - 检测结果缓存和性能优化
 * - 类型安全的语言管理
 * - 检测统计和调试信息
 */
'use client';

import { useLocale } from 'next-intl';
import { useCallback, useEffect, useState } from 'react';

import { usePathname, useRouter } from '@/i18n/routing';
import {
  type LanguageDetectionResult,
  type SupportedLocale,
  detectUserLanguage,
  getDetectionStats,
  saveUserLocalePreference,
} from '@/lib/i18n/language-detection';

/**
 * 智能语言检测 React Hook
 *
 * 功能特性：
 * - 自动检测用户语言偏好
 * - 智能语言切换和持久化
 * - 检测结果缓存和性能优化
 * - 类型安全的语言管理
 * - 检测统计和调试信息
 */

/**
 * 智能语言检测 React Hook
 *
 * 功能特性：
 * - 自动检测用户语言偏好
 * - 智能语言切换和持久化
 * - 检测结果缓存和性能优化
 * - 类型安全的语言管理
 * - 检测统计和调试信息
 */

/**
 * 智能语言检测 React Hook
 *
 * 功能特性：
 * - 自动检测用户语言偏好
 * - 智能语言切换和持久化
 * - 检测结果缓存和性能优化
 * - 类型安全的语言管理
 * - 检测统计和调试信息
 */

/**
 * 智能语言检测 React Hook
 *
 * 功能特性：
 * - 自动检测用户语言偏好
 * - 智能语言切换和持久化
 * - 检测结果缓存和性能优化
 * - 类型安全的语言管理
 * - 检测统计和调试信息
 */

/**
 * 智能语言检测 React Hook
 *
 * 功能特性：
 * - 自动检测用户语言偏好
 * - 智能语言切换和持久化
 * - 检测结果缓存和性能优化
 * - 类型安全的语言管理
 * - 检测统计和调试信息
 */

// Hook 返回值接口
export interface SmartLanguageDetectionHook {
  // 当前语言状态
  currentLocale: SupportedLocale;
  detectionResult: LanguageDetectionResult | null;
  isDetecting: boolean;

  // 语言切换功能
  switchLanguage: (locale: SupportedLocale, savePreference?: boolean) => void;
  detectAndSwitch: () => Promise<void>;

  // 统计和调试信息
  detectionStats: ReturnType<typeof getDetectionStats>;
  refreshStats: () => void;

  // 实用工具
  isAutoDetected: boolean;
  canAutoDetect: boolean;
}

// Hook 配置选项
export interface SmartLanguageDetectionOptions {
  // 是否在组件挂载时自动检测
  autoDetectOnMount?: boolean;

  // 是否自动保存用户选择的语言偏好
  autoSavePreference?: boolean;

  // 检测结果缓存时间（毫秒）
  cacheTimeout?: number;

  // 是否启用调试模式
  debug?: boolean;
}

// 默认配置
const DEFAULT_OPTIONS: Required<SmartLanguageDetectionOptions> = {
  autoDetectOnMount: true,
  autoSavePreference: true,
  cacheTimeout: 5 * 60 * 1000, // 5分钟
  debug: false,
};

/**
 * 智能语言检测 Hook
 */
export function useSmartLanguageDetection(
  options: SmartLanguageDetectionOptions = {}
): SmartLanguageDetectionHook {
  const config = { ...DEFAULT_OPTIONS, ...options };

  // Next-intl hooks
  const currentLocale = useLocale() as SupportedLocale;
  const router = useRouter();
  const pathname = usePathname();

  // 状态管理
  const [detectionResult, setDetectionResult] =
    useState<LanguageDetectionResult | null>(null);
  const [isDetecting, setIsDetecting] = useState(false);
  const [detectionStats, setDetectionStats] = useState(() =>
    getDetectionStats()
  );
  const [isAutoDetected, setIsAutoDetected] = useState(false);

  // 刷新统计信息
  const refreshStats = useCallback(() => {
    setDetectionStats(getDetectionStats());
  }, []);

  // 语言切换功能
  const switchLanguage = useCallback(
    (
      locale: SupportedLocale,
      savePreference: boolean = config.autoSavePreference
    ) => {
      if (config.debug && process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
            console.log(`[SmartLanguageDetection] Switching to locale: ${locale}`);
          }
        }
      }

      // 保存用户偏好
      if (savePreference) {
        saveUserLocalePreference(locale, 'manual');
        refreshStats();
      }

      // 执行路由切换
      router.replace(pathname, { locale });

      // 更新自动检测标记
      setIsAutoDetected(false);
    },
    [config.autoSavePreference, config.debug, pathname, router, refreshStats]
  );

  // 执行智能检测并切换
  const detectAndSwitch = useCallback(async () => {
    if (isDetecting) return;

    setIsDetecting(true);

    try {
      if (config.debug && process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
            console.log('[SmartLanguageDetection] Starting language detection...');
          }
        }
      }

      // 执行检测
      const result = detectUserLanguage(pathname);
      setDetectionResult(result);

      if (config.debug && process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
            console.log('[SmartLanguageDetection] Detection result:', result);
          }
        }
      }

      // 如果检测到的语言与当前语言不同，且置信度足够高
      if (result.locale !== currentLocale && result.confidence >= 0.7) {
        if (config.debug && process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
            console.log(
            `[SmartLanguageDetection] Auto-switching from ${currentLocale} to ${result.locale}`
          );
          }
        }

        // 自动切换语言
        switchLanguage(result.locale, result.source !== 'url');
        setIsAutoDetected(true);
      }

      refreshStats();
    } catch (error) {
      if (config.debug && process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
            console.error('[SmartLanguageDetection] Detection failed:', error);
          }
        }
      }
    } finally {
      setIsDetecting(false);
    }
  }, [
    isDetecting,
    config.debug,
    pathname,
    currentLocale,
    switchLanguage,
    refreshStats,
  ]);

  // 检查是否可以进行自动检测
  const canAutoDetect = useCallback(() => {
    // 在服务端渲染时不能检测
    if (typeof window === 'undefined') return false;

    // 如果已经在检测中，不能重复检测
    if (isDetecting) return false;

    // 如果已经自动检测过，不再重复检测
    if (isAutoDetected) return false;

    return true;
  }, [isDetecting, isAutoDetected]);

  // 组件挂载时的自动检测
  useEffect(() => {
    if (!config.autoDetectOnMount) return;
    if (!canAutoDetect()) return;

    // 延迟执行，避免阻塞初始渲染
    const timer = setTimeout(() => {
      void detectAndSwitch();
    }, 100);

    return () => clearTimeout(timer);
  }, [config.autoDetectOnMount, canAutoDetect, detectAndSwitch]);

  // 监听路由变化，重置自动检测状态
  useEffect(() => {
    setIsAutoDetected(false);
  }, [pathname]);

  // 监听语言变化，更新检测结果
  useEffect(() => {
    if (detectionResult !== null && detectionResult !== undefined && detectionResult.locale !== currentLocale) {
      // 如果当前语言与检测结果不同，清除检测结果
      setDetectionResult(null);
    }
  }, [currentLocale, detectionResult]);

  // 开发模式下的调试信息
  useEffect(() => {
    if (config.debug && typeof window !== 'undefined') {
      (window as any).__smartLanguageDetection = {
        currentLocale,
        detectionResult,
        isDetecting,
        detectionStats,
        isAutoDetected,
        canAutoDetect: canAutoDetect(),
      };
    }
  }, [
    config.debug,
    currentLocale,
    detectionResult,
    isDetecting,
    detectionStats,
    isAutoDetected,
    canAutoDetect,
  ]);

  return {
    // 当前状态
    currentLocale,
    detectionResult,
    isDetecting,

    // 功能方法
    switchLanguage,
    detectAndSwitch,

    // 统计信息
    detectionStats,
    refreshStats,

    // 实用属性
    isAutoDetected,
    canAutoDetect: canAutoDetect(),
  };
}

/**
 * 简化版语言检测 Hook（仅提供基础功能）
 */
export function useLanguageDetection() {
  return useSmartLanguageDetection({
    autoDetectOnMount: true,
    autoSavePreference: true,
    debug: false,
  });
}

/**
 * 调试版语言检测 Hook（启用详细日志）
 */
export function useLanguageDetectionDebug() {
  return useSmartLanguageDetection({
    autoDetectOnMount: true,
    autoSavePreference: true,
    debug: true,
  });
}
