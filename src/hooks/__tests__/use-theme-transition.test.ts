/**
 * 主题过渡 React Hook 测试
 *
 * 测试覆盖：
 * - Hook 初始化和状态管理
 * - 过渡动画控制
 * - 配置更新和管理
 * - 事件处理和回调
 * - 错误处理
 */
import { beforeEach, describe, expect, it, vi } from 'vitest';

import type { TransitionConfig } from '../../lib/theme/theme-transition-manager';
import { useThemeTransition } from '../use-theme-transition';

// Mock next-themes
const mockSetTheme = vi.fn();
const mockTheme = 'light';

vi.mock('next-themes', () => ({
  useTheme: () => ({
    theme: mockTheme,
    setTheme: mockSetTheme,
  }),
}));

// Mock React hooks
const mockState: any = {};
const mockSetState: any = vi.fn();

vi.mock('react', () => ({
  useState: vi.fn((initial) => [initial, mockSetState]),
  useEffect: vi.fn((fn) => fn()),
  useCallback: vi.fn((fn) => fn),
  useMemo: vi.fn((fn) => fn()),
  useRef: vi.fn(() => ({ current: null })),
}));

// Mock requestAnimationFrame
let animationFrameId = 0;
const mockRequestAnimationFrame = vi.fn((callback: FrameRequestCallback) => {
  animationFrameId++;
  setTimeout(() => callback(performance.now()), 16);
  return animationFrameId;
});

const mockCancelAnimationFrame = vi.fn((id: number) => {
  // Mock implementation
});

// Mock performance API
const mockPerformance = {
  now: vi.fn(() => Date.now()),
  mark: vi.fn(),
  measure: vi.fn(),
};

// Mock DOM APIs
const mockElement = {
  style: {
    setProperty: vi.fn(),
    removeProperty: vi.fn(),
  },
  classList: {
    add: vi.fn(),
    remove: vi.fn(),
  },
  getAttribute: vi.fn(),
  setAttribute: vi.fn(),
  appendChild: vi.fn(),
  removeChild: vi.fn(),
};

const mockDocument = {
  documentElement: mockElement,
  createElement: vi.fn(() => mockElement),
  body: mockElement,
};

const mockWindow = {
  requestAnimationFrame: mockRequestAnimationFrame,
  cancelAnimationFrame: mockCancelAnimationFrame,
  performance: mockPerformance,
  matchMedia: vi.fn(() => ({
    matches: false,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
  })),
  getComputedStyle: vi.fn(() => ({
    getPropertyValue: vi.fn(() => 'hsl(0, 0%, 100%)'),
  })),
  document: mockDocument,
};

// Mock globals
Object.defineProperty(global, 'window', { value: mockWindow });
Object.defineProperty(global, 'document', { value: mockDocument });
Object.defineProperty(global, 'requestAnimationFrame', {
  value: mockRequestAnimationFrame,
});
Object.defineProperty(global, 'cancelAnimationFrame', {
  value: mockCancelAnimationFrame,
});
Object.defineProperty(global, 'performance', { value: mockPerformance });

// Mock DOM environment for React Testing Library
Object.defineProperty(global, 'HTMLElement', {
  value: class MockHTMLElement {
    appendChild = vi.fn();
    removeChild = vi.fn();
    style = { setProperty: vi.fn(), removeProperty: vi.fn() };
    classList = { add: vi.fn(), remove: vi.fn() };
  },
});

// Setup minimal DOM environment
if (typeof global.document === 'undefined') {
  global.document = mockDocument as any;
  global.window = mockWindow as any;
}

describe('主题过渡 React Hook', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Hook 基础功能', () => {
    it('应该正确导入和调用', () => {
      expect(typeof useThemeTransition).toBe('function');

      // 简单调用测试
      const result = useThemeTransition();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(2);

      const [state, actions] = result;
      expect(typeof state).toBe('object');
      expect(typeof actions).toBe('object');
    });

    it('应该接受自定义配置', () => {
      const customConfig: Partial<TransitionConfig> = {
        type: 'slide',
        duration: 500,
        easing: 'ease-in',
        respectMotionPreference: false,
      };

      const { result } = renderHook(() =>
        useThemeTransition({
          config: customConfig,
          enabled: false,
        })
      );

      const [state] = result.current;

      expect(state.enabled).toBe(false);
      expect(state.respectMotionPreference).toBe(false);
    });

    it('应该检测用户动画偏好', () => {
      const mockMediaQuery = {
        matches: true,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      mockWindow.matchMedia.mockReturnValue(mockMediaQuery);

      const { result } = renderHook(() => useThemeTransition());

      expect(mockWindow.matchMedia).toHaveBeenCalledWith(
        '(prefers-reduced-motion: reduce)'
      );
    });
  });

  describe('过渡动画控制', () => {
    it('应该开始过渡动画', async () => {
      const { result } = renderHook(() => useThemeTransition());

      const [, actions] = result.current;

      await act(async () => {
        await actions.startTransition('light', 'dark');
      });

      // 验证过渡已完成（在测试环境中会立即完成）
      const [state] = result.current;
      expect(state.fromTheme).toBe('light');
      expect(state.toTheme).toBe('dark');
    });

    it('应该取消过渡动画', () => {
      const { result } = renderHook(() => useThemeTransition());

      const [, actions] = result.current;

      act(() => {
        actions.cancelTransition();
      });

      const [state] = result.current;
      expect(state.isAnimating).toBe(false);
    });

    it('应该在禁用时跳过过渡', async () => {
      const { result } = renderHook(() =>
        useThemeTransition({
          enabled: false,
        })
      );

      const [, actions] = result.current;

      await act(async () => {
        await actions.startTransition('light', 'dark');
      });

      // 应该没有状态变化
      const [state] = result.current;
      expect(state.fromTheme).toBe('');
      expect(state.toTheme).toBe('');
    });
  });

  describe('配置管理', () => {
    it('应该更新过渡配置', () => {
      const { result } = renderHook(() => useThemeTransition());

      const [, actions] = result.current;

      act(() => {
        actions.updateConfig({
          type: 'scale',
          duration: 800,
        });
      });

      // 配置应该已更新（通过内部管理器）
      expect(actions.updateConfig).toBeDefined();
    });

    it('应该处理配置变化的副作用', () => {
      const { result, rerender } = renderHook(
        ({ config }) => useThemeTransition({ config }),
        {
          initialProps: {
            config: { type: 'fade' as const, duration: 300 },
          },
        }
      );

      // 更新配置
      rerender({
        config: { type: 'slide' as const, duration: 500 },
      });

      // Hook 应该处理配置变化
      const [state] = result.current;
      expect(state).toBeDefined();
    });
  });

  describe('事件处理', () => {
    it('应该处理过渡开始事件', async () => {
      const onStart = vi.fn();

      const { result } = renderHook(() =>
        useThemeTransition({
          onStart,
        })
      );

      const [, actions] = result.current;

      await act(async () => {
        await actions.startTransition('light', 'dark');
      });

      expect(onStart).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'start',
          fromTheme: 'light',
          toTheme: 'dark',
        })
      );
    });

    it('应该处理过渡完成事件', async () => {
      const onComplete = vi.fn();

      const { result } = renderHook(() =>
        useThemeTransition({
          onComplete,
        })
      );

      const [, actions] = result.current;

      await act(async () => {
        await actions.startTransition('light', 'dark');
      });

      expect(onComplete).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'complete',
          fromTheme: 'light',
          toTheme: 'dark',
        })
      );
    });

    it('应该处理过渡取消事件', () => {
      const onCancel = vi.fn();

      const { result } = renderHook(() =>
        useThemeTransition({
          onCancel,
        })
      );

      const [, actions] = result.current;

      // 手动设置动画状态以触发取消事件
      act(() => {
        // 模拟正在进行的动画
        const manager = result.current[1] as any;
        if (manager.manager) {
          manager.manager.animationState = {
            isAnimating: true,
            currentAnimation: 'test',
            progress: 0.5,
            startTime: Date.now(),
            endTime: Date.now() + 1000,
            fromTheme: 'light',
            toTheme: 'dark',
          };
        }

        actions.cancelTransition();
      });

      expect(onCancel).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'cancel',
        })
      );
    });
  });

  describe('性能监控', () => {
    it('应该提供性能指标', () => {
      const { result } = renderHook(() => useThemeTransition());

      const [, actions] = result.current;

      const metrics = actions.getPerformanceMetrics();

      if (metrics) {
        expect(typeof metrics.frameRate).toBe('number');
        expect(typeof metrics.droppedFrames).toBe('number');
        expect(typeof metrics.animationDuration).toBe('number');
        expect(typeof metrics.memoryUsage).toBe('number');
        expect(typeof metrics.cpuUsage).toBe('number');
      }
    });
  });

  describe('清理和内存管理', () => {
    it('应该在卸载时清理资源', () => {
      const { result, unmount } = renderHook(() => useThemeTransition());

      const [, actions] = result.current;

      // 开始一个过渡
      act(() => {
        actions.startTransition('light', 'dark');
      });

      // 卸载组件
      unmount();

      // 应该没有错误或内存泄漏
      expect(true).toBe(true);
    });

    it('应该清理事件监听器', () => {
      const onStart = vi.fn();

      const { unmount } = renderHook(() =>
        useThemeTransition({
          onStart,
        })
      );

      // 卸载组件
      unmount();

      // 事件监听器应该被清理
      expect(true).toBe(true);
    });
  });

  describe('错误处理', () => {
    it('应该处理过渡错误', async () => {
      const onError = vi.fn();

      const { result } = renderHook(() =>
        useThemeTransition({
          onError,
        })
      );

      const [, actions] = result.current;

      // 模拟错误情况
      await act(async () => {
        try {
          await actions.startTransition(
            'invalid-theme',
            'another-invalid-theme'
          );
        } catch (error) {
          // 错误应该被捕获
        }
      });

      // 应该没有未处理的错误
      expect(true).toBe(true);
    });

    it('应该在DOM不可用时优雅降级', () => {
      // 临时移除 window
      const originalWindow = global.window;
      delete (global as any).window;

      const { result } = renderHook(() => useThemeTransition());

      const [state] = result.current;

      // 应该仍然可以工作
      expect(state).toBeDefined();

      // 恢复 window
      global.window = originalWindow;
    });
  });
});
