/**
 * 主题过渡动画 React Hook
 *
 * 功能特性：
 * - 主题切换动画控制
 * - 动画状态管理
 * - 性能监控和优化
 * - 用户偏好检测
 * - 事件处理和回调
 */
import { useTheme } from 'next-themes';
import { useCallback, useEffect, useRef, useState } from 'react';

import {
  type AnimationState,
  type PerformanceMetrics,
  ThemeTransitionManager,
  type TransitionConfig,
  type TransitionEvent,
  themeTransitionManager,
} from '@/lib/theme/theme-transition-manager';

// Hook 配置选项
export interface UseThemeTransitionOptions {
  enabled?: boolean; // 是否启用过渡动画
  config?: Partial<TransitionConfig>; // 过渡配置
  onTransitionStart?: (event: TransitionEvent) => void;
  onTransitionProgress?: (event: TransitionEvent) => void;
  onTransitionComplete?: (event: TransitionEvent) => void;
  onTransitionCancel?: (event: TransitionEvent) => void;
  autoDetectMotionPreference?: boolean; // 自动检测动画偏好
  performanceMonitoring?: boolean; // 性能监控
}

// Hook 状态
export interface ThemeTransitionState {
  isAnimating: boolean;
  animationProgress: number;
  currentAnimation: string | null;
  fromTheme: string;
  toTheme: string;
  isReducedMotion: boolean;
  performanceMetrics: PerformanceMetrics | null;
}

// Hook 操作方法
export interface ThemeTransitionActions {
  startTransition: (fromTheme: string, toTheme: string) => Promise<void>;
  cancelTransition: () => void;
  updateConfig: (config: Partial<TransitionConfig>) => void;
  toggleAnimation: () => void;
  resetToDefaults: () => void;
  getPerformanceMetrics: () => PerformanceMetrics | null;
}

/**
 * 主题过渡动画 Hook
 */
export function useThemeTransition(
  options: UseThemeTransitionOptions = {}
): [ThemeTransitionState, ThemeTransitionActions] {
  const {
    enabled = true,
    config = {},
    onTransitionStart,
    onTransitionProgress,
    onTransitionComplete,
    onTransitionCancel,
    autoDetectMotionPreference = true,
    performanceMonitoring = false,
  } = options;

  const { theme, setTheme } = useTheme();
  const managerRef = useRef<ThemeTransitionManager>(themeTransitionManager);
  const previousThemeRef = useRef<string>(theme || 'light');
  const [isReducedMotion, setIsReducedMotion] = useState(false);

  // 动画状态
  const [animationState, setAnimationState] = useState<AnimationState>({
    isAnimating: false,
    currentAnimation: null,
    progress: 0,
    startTime: 0,
    endTime: 0,
    fromTheme: '',
    toTheme: '',
  });

  // 性能指标
  const [performanceMetrics, setPerformanceMetrics] =
    useState<PerformanceMetrics | null>(null);

  /**
   * 检测用户动画偏好
   */
  const detectMotionPreference = useCallback(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setIsReducedMotion(mediaQuery.matches);

    // 监听变化
    const handleChange = (e: MediaQueryListEvent) => {
      setIsReducedMotion(e.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  /**
   * 更新动画状态
   */
  const updateAnimationState = useCallback(() => {
    if (managerRef.current) {
      const state = managerRef.current.getAnimationState();
      setAnimationState(state);
    }
  }, []);

  /**
   * 更新性能指标
   */
  const updatePerformanceMetrics = useCallback(() => {
    if (performanceMonitoring && managerRef.current) {
      const metrics = managerRef.current.getPerformanceMetrics();
      setPerformanceMetrics(metrics);
    }
  }, [performanceMonitoring]);

  /**
   * 事件处理器
   */
  const handleTransitionStart = useCallback(
    (event: TransitionEvent) => {
      updateAnimationState();
      onTransitionStart?.(event);
    },
    [updateAnimationState, onTransitionStart]
  );

  const handleTransitionProgress = useCallback(
    (event: TransitionEvent) => {
      updateAnimationState();
      updatePerformanceMetrics();
      onTransitionProgress?.(event);
    },
    [updateAnimationState, updatePerformanceMetrics, onTransitionProgress]
  );

  const handleTransitionComplete = useCallback(
    (event: TransitionEvent) => {
      updateAnimationState();
      updatePerformanceMetrics();
      onTransitionComplete?.(event);
    },
    [updateAnimationState, updatePerformanceMetrics, onTransitionComplete]
  );

  const handleTransitionCancel = useCallback(
    (event: TransitionEvent) => {
      updateAnimationState();
      onTransitionCancel?.(event);
    },
    [updateAnimationState, onTransitionCancel]
  );

  /**
   * 开始过渡动画
   */
  const startTransition = useCallback(
    async (fromTheme: string, toTheme: string) => {
      if (!enabled) {
        return;
      }

      try {
        if (managerRef.current) {
          await managerRef.current.startTransition(fromTheme, toTheme);
        }
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
            console.error('Failed to start theme transition:', error);
          }
        }
      }
    },
    [enabled]
  );

  /**
   * 取消过渡动画
   */
  const cancelTransition = useCallback(() => {
    if (managerRef.current) {
      managerRef.current.cancelTransition();
    }
  }, []);

  /**
   * 更新配置
   */
  const updateConfig = useCallback((newConfig: Partial<TransitionConfig>) => {
    if (managerRef.current) {
      managerRef.current.updateConfig(newConfig);
    }
  }, []);

  /**
   * 切换动画开关
   */
  const toggleAnimation = useCallback(() => {
    if (managerRef.current) {
      const currentConfig = managerRef.current.getConfig();
      const newType = currentConfig.type === 'none' ? 'fade' : 'none';
      updateConfig({ type: newType });
    }
  }, [updateConfig]);

  /**
   * 重置为默认配置
   */
  const resetToDefaults = useCallback(() => {
    updateConfig({
      type: 'fade',
      duration: 300,
      delay: 0,
      easing: 'ease-out',
      respectMotionPreference: true,
    });
  }, [updateConfig]);

  /**
   * 获取性能指标
   */
  const getPerformanceMetrics = useCallback((): PerformanceMetrics | null => {
    if (managerRef.current) {
      return managerRef.current.getPerformanceMetrics();
    }
    return null;
  }, []);

  /**
   * 自动主题切换处理
   */
  const handleThemeChange = useCallback(
    async (newTheme: string) => {
      const previousTheme = previousThemeRef.current;

      if (previousTheme !== newTheme && enabled) {
        await startTransition(previousTheme, newTheme);
      }

      previousThemeRef.current = newTheme;
    },
    [enabled, startTransition]
  );

  // 初始化效果
  useEffect(() => {
    // 检测动画偏好
    if (autoDetectMotionPreference) {
      return detectMotionPreference();
    }

    // 如果不自动检测，返回空的清理函数
    return () => {};
  }, [autoDetectMotionPreference, detectMotionPreference]);

  // 配置更新效果
  useEffect(() => {
    if (Object.keys(config).length > 0) {
      updateConfig(config);
    }
  }, [config, updateConfig]);

  // 事件监听器设置
  useEffect(() => {
    const manager = managerRef.current;

    if (!manager) {
      return;
    }

    manager.addEventListener('start', handleTransitionStart);
    manager.addEventListener('progress', handleTransitionProgress);
    manager.addEventListener('complete', handleTransitionComplete);
    manager.addEventListener('cancel', handleTransitionCancel);

    return () => {
      if (manager) {
        manager.removeEventListener('start', handleTransitionStart);
        manager.removeEventListener('progress', handleTransitionProgress);
        manager.removeEventListener('complete', handleTransitionComplete);
        manager.removeEventListener('cancel', handleTransitionCancel);
      }
    };
  }, [
    handleTransitionStart,
    handleTransitionProgress,
    handleTransitionComplete,
    handleTransitionCancel,
  ]);

  // 主题变化监听
  useEffect(() => {
    if (theme) {
      handleThemeChange(theme);
    }
  }, [theme, handleThemeChange]);

  // 性能监控定时器
  useEffect(() => {
    if (performanceMonitoring) {
      const interval = setInterval(updatePerformanceMetrics, 1000);
      return () => clearInterval(interval);
    }

    // 如果不启用性能监控，返回空的清理函数
    return () => {};
  }, [performanceMonitoring, updatePerformanceMetrics]);

  // 组件卸载清理
  useEffect(() => {
    return () => {
      cancelTransition();
    };
  }, [cancelTransition]);

  // 构建状态对象
  const state: ThemeTransitionState = {
    isAnimating: animationState.isAnimating,
    animationProgress: animationState.progress,
    currentAnimation: animationState.currentAnimation,
    fromTheme: animationState.fromTheme,
    toTheme: animationState.toTheme,
    isReducedMotion,
    performanceMetrics,
  };

  // 构建操作对象
  const actions: ThemeTransitionActions = {
    startTransition,
    cancelTransition,
    updateConfig,
    toggleAnimation,
    resetToDefaults,
    getPerformanceMetrics,
  };

  return [state, actions];
}

/**
 * 简化版主题过渡 Hook（仅基本功能）
 */
export function useSimpleThemeTransition(enabled = true) {
  const [state, actions] = useThemeTransition({
    enabled,
    config: {
      type: 'fade',
      duration: 200,
      easing: 'ease-out',
    },
    autoDetectMotionPreference: true,
  });

  return {
    isAnimating: state.isAnimating,
    progress: state.animationProgress,
    startTransition: actions.startTransition,
    cancelTransition: actions.cancelTransition,
  };
}

/**
 * 高性能主题过渡 Hook（包含性能监控）
 */
export function usePerformantThemeTransition(
  options: UseThemeTransitionOptions = {}
) {
  return useThemeTransition({
    ...options,
    performanceMonitoring: true,
    config: {
      type: 'fade',
      duration: 150,
      easing: 'ease-out',
      respectMotionPreference: true,
      ...options.config,
    },
  });
}

/**
 * 自定义动画主题过渡 Hook
 */
export function useCustomThemeTransition(
  transitionType: TransitionConfig['type'] = 'fade',
  duration = 300
) {
  return useThemeTransition({
    config: {
      type: transitionType,
      duration,
      easing: 'ease-out',
      respectMotionPreference: true,
    },
    autoDetectMotionPreference: true,
  });
}
