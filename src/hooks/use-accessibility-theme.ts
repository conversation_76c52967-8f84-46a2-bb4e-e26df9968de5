/**
 * 无障碍主题 React Hook
 * 
 * 功能特性：
 * - 无障碍配置状态管理
 * - 系统偏好检测
 * - 配置更新操作
 * - 事件监听和回调
 * - 自动应用系统推荐
 */

import { useCallback, useEffect, useState } from 'react';

import {
  type AccessibilityConfig,
  type AccessibilityEvent,
  type SystemPreferences,
  accessibilityThemeManager,
} from '@/lib/theme/accessibility-theme-manager';

// Hook 配置选项
export interface UseAccessibilityThemeOptions {
  autoApplySystemPreferences?: boolean; // 自动应用系统偏好
  onConfigChange?: (config: AccessibilityConfig) => void;
  onHighContrastToggle?: (enabled: boolean) => void;
  onMotionReduced?: (reduced: boolean) => void;
  onFontSizeChange?: (scale: number) => void;
  onColorBlindModeChange?: (type: AccessibilityConfig['colorBlindType']) => void;
  onScreenReaderDetected?: () => void;
}

// Hook 状态
export interface AccessibilityThemeState {
  config: AccessibilityConfig;
  systemPreferences: SystemPreferences;
  isReady: boolean;
  isHighContrast: boolean;
  isReducedMotion: boolean;
  isLargeText: boolean;
  isColorBlindFriendly: boolean;
  isScreenReaderOptimized: boolean;
  fontSize: number;
  contrastLevel: AccessibilityConfig['contrastLevel'];
  colorBlindType: AccessibilityConfig['colorBlindType'];
}

// Hook 操作方法
export interface AccessibilityThemeActions {
  updateConfig: (updates: Partial<AccessibilityConfig>) => void;
  toggleHighContrast: () => void;
  toggleReducedMotion: () => void;
  toggleLargeText: () => void;
  setFontScale: (scale: number) => void;
  setContrastLevel: (level: AccessibilityConfig['contrastLevel']) => void;
  setColorBlindMode: (type: AccessibilityConfig['colorBlindType']) => void;
  resetToDefaults: () => void;
  applySystemRecommendations: () => void;
}

/**
 * 无障碍主题 Hook
 */
export function useAccessibilityTheme(
  options: UseAccessibilityThemeOptions = {}
): [AccessibilityThemeState, AccessibilityThemeActions] {
  const {
    autoApplySystemPreferences = false,
    onConfigChange,
    onHighContrastToggle,
    onMotionReduced,
    onFontSizeChange,
    onColorBlindModeChange,
    onScreenReaderDetected,
  } = options;

  // 状态管理
  const [config, setConfig] = useState<AccessibilityConfig>(
    accessibilityThemeManager.getConfig()
  );
  const [systemPreferences, setSystemPreferences] = useState<SystemPreferences>(
    accessibilityThemeManager.getSystemPreferences()
  );
  const [isReady, setIsReady] = useState(accessibilityThemeManager.isReady());

  /**
   * 处理配置变化事件
   */
  const handleAccessibilityChange = useCallback(
    (event: Event) => {
      const customEvent = event as CustomEvent<AccessibilityEvent>;
      const { type, config: newConfig } = customEvent.detail;

      setConfig(newConfig);
      setSystemPreferences(accessibilityThemeManager.getSystemPreferences());

      // 调用相应的回调函数
      switch (type) {
        case 'config-changed':
          onConfigChange?.(newConfig);
          break;
        case 'high-contrast-toggled':
          onHighContrastToggle?.(newConfig.highContrast);
          break;
        case 'motion-reduced':
          onMotionReduced?.(newConfig.reducedMotion);
          break;
        case 'font-size-changed':
          onFontSizeChange?.(newConfig.fontSize);
          break;
        case 'color-blind-mode-changed':
          onColorBlindModeChange?.(newConfig.colorBlindType);
          break;
        case 'screen-reader-detected':
          onScreenReaderDetected?.();
          break;
      }
    },
    [
      onConfigChange,
      onHighContrastToggle,
      onMotionReduced,
      onFontSizeChange,
      onColorBlindModeChange,
      onScreenReaderDetected,
    ]
  );

  /**
   * 设置事件监听器
   */
  useEffect(() => {
    accessibilityThemeManager.addEventListener('accessibility-change', handleAccessibilityChange);

    // 初始化时检查是否需要自动应用系统偏好
    if (autoApplySystemPreferences) {
      accessibilityThemeManager.applySystemRecommendations();
    }

    // 更新就绪状态
    setIsReady(accessibilityThemeManager.isReady());

    return () => {
      accessibilityThemeManager.removeEventListener('accessibility-change', handleAccessibilityChange);
    };
  }, [handleAccessibilityChange, autoApplySystemPreferences]);

  /**
   * 更新配置
   */
  const updateConfig = useCallback((updates: Partial<AccessibilityConfig>) => {
    accessibilityThemeManager.updateConfig(updates);
  }, []);

  /**
   * 切换高对比度模式
   */
  const toggleHighContrast = useCallback(() => {
    accessibilityThemeManager.toggleHighContrast();
  }, []);

  /**
   * 切换减少动画模式
   */
  const toggleReducedMotion = useCallback(() => {
    accessibilityThemeManager.toggleReducedMotion();
  }, []);

  /**
   * 切换大字体模式
   */
  const toggleLargeText = useCallback(() => {
    accessibilityThemeManager.toggleLargeText();
  }, []);

  /**
   * 设置字体缩放
   */
  const setFontScale = useCallback((scale: number) => {
    accessibilityThemeManager.setFontScale(scale);
  }, []);

  /**
   * 设置对比度级别
   */
  const setContrastLevel = useCallback((level: AccessibilityConfig['contrastLevel']) => {
    accessibilityThemeManager.setContrastLevel(level);
  }, []);

  /**
   * 设置色盲友好模式
   */
  const setColorBlindMode = useCallback((type: AccessibilityConfig['colorBlindType']) => {
    accessibilityThemeManager.setColorBlindMode(type);
  }, []);

  /**
   * 重置为默认设置
   */
  const resetToDefaults = useCallback(() => {
    accessibilityThemeManager.resetToDefaults();
  }, []);

  /**
   * 应用系统推荐设置
   */
  const applySystemRecommendations = useCallback(() => {
    accessibilityThemeManager.applySystemRecommendations();
  }, []);

  // 构建状态对象
  const state: AccessibilityThemeState = {
    config,
    systemPreferences,
    isReady,
    isHighContrast: config.highContrast,
    isReducedMotion: config.reducedMotion,
    isLargeText: config.largeText,
    isColorBlindFriendly: config.colorBlindFriendly,
    isScreenReaderOptimized: config.screenReaderOptimized,
    fontSize: config.fontSize,
    contrastLevel: config.contrastLevel,
    colorBlindType: config.colorBlindType,
  };

  // 构建操作对象
  const actions: AccessibilityThemeActions = {
    updateConfig,
    toggleHighContrast,
    toggleReducedMotion,
    toggleLargeText,
    setFontScale,
    setContrastLevel,
    setColorBlindMode,
    resetToDefaults,
    applySystemRecommendations,
  };

  return [state, actions];
}

/**
 * 简化版无障碍主题 Hook（仅基本功能）
 */
export function useSimpleAccessibilityTheme() {
  const [state, actions] = useAccessibilityTheme({
    autoApplySystemPreferences: true,
  });

  return {
    isHighContrast: state.isHighContrast,
    isReducedMotion: state.isReducedMotion,
    isLargeText: state.isLargeText,
    fontSize: state.fontSize,
    toggleHighContrast: actions.toggleHighContrast,
    toggleReducedMotion: actions.toggleReducedMotion,
    toggleLargeText: actions.toggleLargeText,
    setFontScale: actions.setFontScale,
  };
}

/**
 * 系统偏好检测 Hook
 */
export function useSystemAccessibilityPreferences() {
  const [state] = useAccessibilityTheme();
  
  return {
    systemPreferences: state.systemPreferences,
    hasReducedMotionPreference: state.systemPreferences.prefersReducedMotion,
    hasHighContrastPreference: state.systemPreferences.prefersHighContrast,
    hasForcedColors: state.systemPreferences.forcedColors,
    systemFontSize: state.systemPreferences.fontSize,
    colorSchemePreference: state.systemPreferences.prefersColorScheme,
  };
}
