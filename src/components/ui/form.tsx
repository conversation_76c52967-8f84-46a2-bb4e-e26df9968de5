import { Slot } from '@radix-ui/react-slot';
import * as React from 'react';
import type { ReactElement } from 'react';
import {
  Controller,
  type ControllerProps,
  type FieldError,
  type FieldPath,
  type FieldValues,
  FormProvider,
  useFormContext,
} from 'react-hook-form';

import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

/**
 * 表单组件封装
 *
 * 基于 React Hook Form 和 Radix UI 构建的企业级表单组件系统。
 * 提供类型安全的表单处理、验证和错误显示功能。
 *
 * 特性：
 * - 完全类型安全的表单处理
 * - 自动错误状态管理
 * - 无障碍访问支持
 * - 灵活的组件组合
 * - 与 shadcn/ui 组件完美集成
 */

const Form = FormProvider;

type FormFieldContextValue<
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
> = {
  name: TName;
};

const FormFieldContext = React.createContext<FormFieldContextValue>(
  {} as FormFieldContextValue
);

const FormField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  ...props
}: ControllerProps<TFieldValues, TName>): React.JSX.Element => {
  return (
    <FormFieldContext.Provider value={{ name: props.name }}>
      <Controller {...props} />
    </FormFieldContext.Provider>
  );
};

const useFormField = (): {
  id: string;
  name: string;
  formItemId: string;
  formDescriptionId: string;
  formMessageId: string;
  invalid: boolean;
  isDirty: boolean;
  isTouched: boolean;
  isValidating: boolean;
  error?: FieldError;
} => {
  const fieldContext = React.useContext(FormFieldContext);
  const itemContext = React.useContext(FormItemContext);
  const { getFieldState, formState } = useFormContext();

  const fieldState = getFieldState(fieldContext.name, formState);

  if (fieldContext === null || fieldContext === undefined) {
    throw new Error('useFormField should be used within <FormField>');
  }

  const { id } = itemContext;

  return {
    id,
    name: fieldContext.name,
    formItemId: `${id}-form-item`,
    formDescriptionId: `${id}-form-item-description`,
    formMessageId: `${id}-form-item-message`,
    ...fieldState,
  };
};

type FormItemContextValue = {
  id: string;
};

const FormItemContext = React.createContext<FormItemContextValue>(
  {} as FormItemContextValue
);

const FormItem = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const id = React.useId();

  return (
    <FormItemContext.Provider value={{ id }}>
      <div ref={ref} className={cn('space-y-2', className)} {...props} />
    </FormItemContext.Provider>
  );
});
FormItem.displayName = 'FormItem';

const FormLabel = React.forwardRef<
  React.ElementRef<typeof Label>,
  React.ComponentPropsWithoutRef<typeof Label>
>(({ className, ...props }, ref) => {
  const formField = useFormField();

  return (
    <Label
      ref={ref}
      className={cn(
        formField.error !== undefined && 'text-destructive',
        className
      )}
      htmlFor={formField.formItemId}
      {...props}
    />
  );
});
FormLabel.displayName = 'FormLabel';

const FormControl = React.forwardRef<
  React.ElementRef<typeof Slot>,
  React.ComponentPropsWithoutRef<typeof Slot>
>(({ ...props }, ref) => {
  const formField = useFormField();

  return (
    <Slot
      ref={ref}
      id={formField.formItemId}
      aria-describedby={
        formField.error === undefined
          ? `${formField.formDescriptionId}`
          : `${formField.formDescriptionId} ${formField.formMessageId}`
      }
      aria-invalid={formField.error !== undefined}
      {...props}
    />
  );
});
FormControl.displayName = 'FormControl';

const FormDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => {
  const { formDescriptionId } = useFormField();

  return (
    <p
      ref={ref}
      id={formDescriptionId}
      className={cn('text-muted-foreground text-sm', className)}
      {...props}
    />
  );
});
FormDescription.displayName = 'FormDescription';

const FormMessage = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, children, ...props }, ref) => {
  const formField = useFormField();
  const body =
    formField.error !== undefined
      ? String((formField.error as { message?: string }).message ?? '')
      : children;

  if (body === null || body === undefined || body === '') {
    return null;
  }

  return (
    <p
      ref={ref}
      id={formField.formMessageId}
      className={cn('text-destructive text-sm font-medium', className)}
      {...props}
    >
      {body}
    </p>
  );
});
FormMessage.displayName = 'FormMessage';

/**
 * 表单字段包装器
 *
 * 提供完整的表单字段布局，包括标签、控件、描述和错误消息。
 * 自动处理错误状态和无障碍访问属性。
 */
interface FormFieldWrapperProps {
  label: string;
  description?: string;
  required?: boolean;
  children: ReactElement;
  className?: string;
}

const FormFieldWrapper = React.forwardRef<
  HTMLDivElement,
  FormFieldWrapperProps
>(({ label, description, required, children, className }, ref) => {
  return (
    <FormItem ref={ref} className={className}>
      <FormLabel>
        {label}
        {required === true && <span className='text-destructive ml-1'>*</span>}
      </FormLabel>
      <FormControl>{children}</FormControl>
      {description !== null &&
        description !== undefined &&
        description !== '' && <FormDescription>{description}</FormDescription>}
      <FormMessage />
    </FormItem>
  );
});
FormFieldWrapper.displayName = 'FormFieldWrapper';

/**
 * 表单提交按钮
 *
 * 自动处理提交状态和禁用逻辑。
 */
interface FormSubmitButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isSubmitting?: boolean;
  submittingText?: string;
  children: React.ReactNode;
}

const FormSubmitButton = React.forwardRef<
  HTMLButtonElement,
  FormSubmitButtonProps
>(
  (
    {
      isSubmitting,
      submittingText = '提交中...',
      children,
      disabled,
      ...props
    },
    ref
  ) => {
    return (
      <button
        ref={ref}
        type='submit'
        disabled={disabled === true || isSubmitting === true}
        {...props}
      >
        {isSubmitting === true ? submittingText : children}
      </button>
    );
  }
);
FormSubmitButton.displayName = 'FormSubmitButton';

export {
  useFormField,
  Form,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
  FormField,
  FormFieldWrapper,
  FormSubmitButton,
};
