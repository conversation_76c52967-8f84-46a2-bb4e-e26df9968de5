import { type ReactElement, memo } from 'react';

import {
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

/**
 * 通用表单字段包装器属性
 */
interface FormFieldWrapperProps {
  label: string;
  required?: boolean;
  error?: string;
  children: React.ReactNode;
  htmlFor?: string;
  className?: string;
}

/**
 * 必填标识组件属性
 */
interface RequiredIndicatorProps {
  required?: boolean;
  'aria-label'?: string;
}

/**
 * 必填标识组件
 */
export const RequiredIndicator = memo(
  ({
    required = false,
    'aria-label': ariaLabel = '必填字段',
  }: RequiredIndicatorProps): ReactElement | null => {
    if (!required) return null;

    return (
      <span className='ml-1 text-red-500' aria-label={ariaLabel}>
        *
      </span>
    );
  }
);
RequiredIndicator.displayName = 'RequiredIndicator';

/**
 * 通用表单字段包装器
 *
 * 提供统一的表单字段布局和样式
 */
export const FormFieldWrapper = memo(
  ({
    label,
    required = false,
    error,
    children,
    htmlFor,
    className = '',
  }: FormFieldWrapperProps): ReactElement => {
    return (
      <FormItem className={className}>
        <FormLabel htmlFor={htmlFor}>
          {label}
          <RequiredIndicator required={required} />
        </FormLabel>
        <FormControl>{children}</FormControl>
        {error !== undefined && error !== '' && (
          <FormMessage role='alert'>{error}</FormMessage>
        )}
      </FormItem>
    );
  }
);

FormFieldWrapper.displayName = 'FormFieldWrapper';
