'use client';

import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { ButtonDemo } from '@/components/demo/button-demo';
import { FeatureCards } from '@/components/demo/feature-cards';
import { InputDemo } from '@/components/demo/input-demo';
import { InteractionDemo } from '@/components/demo/interaction-demo';
import { LanguageSwitcher } from '@/components/shared/language-switcher';
import { ThemeSwitcher } from '@/components/shared/theme-switcher';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

/**
