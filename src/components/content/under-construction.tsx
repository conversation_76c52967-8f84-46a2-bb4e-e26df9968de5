'use client';

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Hammer } from 'lucide-react';
import { useTranslations } from 'next-intl';

import { Button } from '@/components/ui/button';
import {
  AnimationContainer,
  ScrollReveal,
} from '@/components/ui/scroll-reveal';
import { Link } from '@/i18n/routing';
import { presetAnimations } from '@/lib/animations';

/**
 * Tailwind CSS 动画类名
 */
const animationClasses = {
  // 浮动动画
  floating: 'animate-bounce',
  // 旋转动画
  rotating: 'animate-spin',
  // 脉冲动画
  pulsing: 'animate-pulse',
  // 渐变动画
  gradient:
    'bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent',
  // 悬停效果
  buttonHover: presetAnimations.buttonHover,
  iconSpin: presetAnimations.iconSpin,
} as const;

/**
 * 建设中图标组件
 */
function ConstructionIcon(): React.JSX.Element {
  return (
    <div className='relative mb-8 flex justify-center'>
      <div className='bg-primary/10 relative rounded-full p-8 motion-safe:animate-bounce motion-reduce:animate-none'>
        <Construction
          className='text-primary h-16 w-16'
          aria-label='建设中图标'
        />

        {/* 装饰性小图标 */}
        <div className='absolute -top-2 -right-2 motion-safe:animate-spin motion-reduce:animate-none'>
          <Hammer
            className='text-muted-foreground h-6 w-6'
            aria-hidden='true'
            style={{ animationDuration: '4s' }}
          />
        </div>
      </div>
    </div>
  );
}

/**
 * 建设中内容组件
 */
function ConstructionContent(): React.JSX.Element {
  const t = useTranslations('underConstruction');

  // 错误边界：检查翻译是否可用
  const safeT = (key: string, fallback: string): string => {
    try {
      const translation = t(key);
      return translation !== '' ? translation : fallback;
    } catch {
      return fallback;
    }
  };

  return (
    <AnimationContainer animation='slideUp' stagger={200}>
      {/* 标题 */}
      <h1 className='text-foreground mb-4 text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl'>
        <span className={animationClasses.gradient}>
          {safeT('title', 'Under Construction')}
        </span>
      </h1>

      {/* 副标题 */}
      <h2 className='text-muted-foreground mb-6 text-xl font-medium sm:text-2xl'>
        {safeT('subtitle', "We're working hard to bring you something amazing")}
      </h2>

      {/* 描述 */}
      <p className='text-muted-foreground mb-10 text-lg leading-relaxed'>
        {safeT(
          'description',
          "This page is currently under development. We're crafting an exceptional experience that will be worth the wait."
        )}
      </p>

      {/* 返回主页按钮 */}
      <div>
        <Button
          asChild
          size='lg'
          className={`group relative overflow-hidden px-8 py-3 text-base font-medium ${animationClasses.buttonHover}`}
        >
          <Link href='/' aria-label={safeT('backToHome', 'Back to Home')}>
            <span className='relative z-10 flex items-center gap-2'>
              <ArrowLeft
                className='h-4 w-4 transition-transform duration-200 group-hover:-translate-x-1'
                aria-hidden='true'
              />
              {safeT('backToHome', 'Back to Home')}
            </span>
            <div className='from-primary to-primary/80 absolute inset-0 bg-gradient-to-r transition-transform duration-200 group-hover:scale-105' />
          </Link>
        </Button>
      </div>

      {/* 即将推出标签 */}
      <div className='mt-12 flex justify-center'>
        <span className='bg-background/50 text-muted-foreground rounded-full border px-4 py-2 text-sm font-medium backdrop-blur-sm motion-safe:animate-pulse motion-reduce:animate-none'>
          {safeT('comingSoon', 'Coming Soon')}
        </span>
      </div>
    </AnimationContainer>
  );
}

/**
 * 建设中页面模板组件
 *
 * 特性：
 * - 优雅的动画过渡效果：使用 Tailwind CSS 动画
 * - 统一的占位符设计：与整体设计保持一致
 * - 返回主页的导航：便于用户导航
 * - 多语言支持：集成 next-intl
 * - 响应式设计：适配各种屏幕尺寸
 * - 企业级视觉效果：专业且美观
 * - 性能优化：零包体积动画库开销
 */
export function UnderConstruction(): React.JSX.Element {
  // 错误边界：确保组件在任何情况下都能渲染
  try {
    return (
      <div className='from-background via-background to-muted/20 relative min-h-screen overflow-hidden bg-gradient-to-br'>
        {/* 背景装饰 */}
        <div className='bg-grid-white/[0.02] absolute inset-0 bg-[size:50px_50px]' />
        <div className='via-primary/5 absolute top-0 left-0 h-full w-full bg-gradient-to-r from-transparent to-transparent' />

        {/* 主要内容 */}
        <div className='relative flex min-h-screen items-center justify-center px-4'>
          <ScrollReveal
            animation='fadeIn'
            className='mx-auto max-w-2xl text-center'
          >
            <ConstructionIcon />
            <ConstructionContent />
          </ScrollReveal>
        </div>

        {/* 底部渐变 */}
        <div className='from-background absolute right-0 bottom-0 left-0 h-20 bg-gradient-to-t to-transparent' />
      </div>
    );
  } catch (error) {
    // 错误回退：提供基本的建设中页面
     
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
        console.error('UnderConstruction component error:', error);
      }
    }
    return (
      <div className='from-background via-background to-muted/20 relative min-h-screen overflow-hidden bg-gradient-to-br'>
        <div className='relative flex min-h-screen items-center justify-center px-4'>
          <div className='mx-auto max-w-2xl text-center'>
            <div className='relative mb-8 flex justify-center'>
              <div className='bg-primary/10 relative rounded-full p-8'>
                <Construction
                  className='text-primary h-16 w-16'
                  aria-label='Under Construction'
                />
              </div>
            </div>
            <h1 className='text-foreground mb-4 text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl'>
              <span className='from-primary to-primary/60 bg-gradient-to-r bg-clip-text text-transparent'>
                Under Construction
              </span>
            </h1>
            <h2 className='text-muted-foreground mb-6 text-xl font-medium sm:text-2xl'>
              We&apos;re working hard to bring you something amazing
            </h2>
            <p className='text-muted-foreground mb-10 text-lg leading-relaxed'>
              This page is currently under development. We&apos;re crafting an
              exceptional experience that will be worth the wait.
            </p>
            <div className='mt-8'>
              <Link
                href='/'
                className='bg-primary text-primary-foreground hover:bg-primary/90 inline-flex items-center gap-2 rounded-lg px-8 py-3 text-base font-medium transition-colors'
                aria-label='Back to Home'
              >
                <ArrowLeft className='h-4 w-4' aria-hidden='true' />
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }
}
