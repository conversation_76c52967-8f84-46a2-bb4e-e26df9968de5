'use client';

import { motion } from 'framer-motion';
import { Globe, Moon, Search, Shield, Smartphone, Zap } from 'lucide-react';
import { useTranslations } from 'next-intl';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

/**
 * 特性数据配置
 */
const features = [
  {
    id: 'responsive',
    icon: Smartphone,
    color: 'text-blue-500',
    bgColor: 'bg-blue-500/10',
  },
  {
    id: 'accessible',
    icon: Shield,
    color: 'text-green-500',
    bgColor: 'bg-green-500/10',
  },
  {
    id: 'performance',
    icon: Zap,
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-500/10',
  },
  {
    id: 'seo',
    icon: Search,
    color: 'text-purple-500',
    bgColor: 'bg-purple-500/10',
  },
  {
    id: 'i18n',
    icon: Globe,
    color: 'text-cyan-500',
    bgColor: 'bg-cyan-500/10',
  },
  {
    id: 'darkMode',
    icon: Moon,
    color: 'text-indigo-500',
    bgColor: 'bg-indigo-500/10',
  },
];

/**
 * 动画配置
 */
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.6,
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.6 },
  },
};

/**
 * 特性展示组件
 *
 * 特性：
 * - 六大核心特性：响应式、无障碍、性能、SEO、国际化、深色模式
 * - 网格布局：响应式的卡片网格
 * - 图标展示：每个特性都有对应的图标
 * - 动画效果：滚动触发和悬停效果
 * - 多语言支持：完整的国际化内容
 */
export function FeaturesSection(): React.JSX.Element {
  const t = useTranslations('home.features');

  return (
    <section className='py-20 md:py-32'>
      <div className='container mx-auto px-4'>
        <motion.div
          className='mx-auto max-w-4xl text-center'
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className='text-foreground mb-4 text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl'>
            {t('title')}
          </h2>
          <p className='text-muted-foreground mb-16 text-lg'>{t('subtitle')}</p>
        </motion.div>

        <motion.div
          className='grid gap-8 md:grid-cols-2 lg:grid-cols-3'
          variants={containerVariants}
          initial='hidden'
          whileInView='visible'
          viewport={{ once: true }}
        >
          {features.map((feature) => {
            const IconComponent = feature.icon;
            return (
              <motion.div key={feature.id} variants={itemVariants}>
                <Card className='group hover:shadow-primary/5 h-full transition-all duration-300 hover:-translate-y-1 hover:shadow-lg'>
                  <CardHeader className='text-center'>
                    <div
                      className={`mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full ${feature.bgColor} transition-all duration-300 group-hover:scale-110`}
                    >
                      <IconComponent className={`h-8 w-8 ${feature.color}`} />
                    </div>
                    <CardTitle className='text-xl'>
                      {t(`items.${feature.id}.title`)}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className='text-center leading-relaxed'>
                      {t(`items.${feature.id}.description`)}
                    </CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </motion.div>

        {/* 底部 CTA */}
        <motion.div
          className='mt-20 text-center'
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
        >
          <div className='mx-auto max-w-2xl'>
            <h3 className='text-foreground mb-4 text-2xl font-bold'>
              {t('cta.title')}
            </h3>
            <p className='text-muted-foreground'>{t('cta.description')}</p>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
