import type { ReactElement } from 'react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

export function InputDemo(): ReactElement {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Input 和 Label 组件</CardTitle>
        <CardDescription>测试表单输入组件的样式和交互</CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
          <div className='space-y-2'>
            <Label htmlFor='name'>姓名</Label>
            <Input id='name' placeholder='请输入您的姓名' />
          </div>
          <div className='space-y-2'>
            <Label htmlFor='email'>邮箱</Label>
            <Input id='email' type='email' placeholder='请输入您的邮箱' />
          </div>
          <div className='space-y-2'>
            <Label htmlFor='password'>密码</Label>
            <Input id='password' type='password' placeholder='请输入密码' />
          </div>
          <div className='space-y-2'>
            <Label htmlFor='disabled'>禁用状态</Label>
            <Input id='disabled' placeholder='禁用的输入框' disabled />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
