import type { ReactElement } from 'react';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export function ButtonDemo(): ReactElement {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Button 组件</CardTitle>
        <CardDescription>测试不同变体和尺寸的按钮组件</CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='flex flex-wrap gap-4'>
          <Button variant='default'>Default</Button>
          <Button variant='secondary'>Secondary</Button>
          <Button variant='outline'>Outline</Button>
          <Button variant='ghost'>Ghost</Button>
          <Button variant='destructive'>Destructive</Button>
          <Button variant='link'>Link</Button>
        </div>
        <div className='flex flex-wrap gap-4'>
          <Button size='sm'>Small</Button>
          <Button size='default'>Default</Button>
          <Button size='lg'>Large</Button>
          <Button size='icon'>🚀</Button>
        </div>
      </CardContent>
    </Card>
  );
}
