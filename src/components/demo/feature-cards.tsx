import { useTranslations } from 'next-intl';
import type { ReactElement } from 'react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

export function FeatureCards(): ReactElement {
  const t = useTranslations('home.demo.featureCards');

  return (
    <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
      <Card>
        <CardHeader>
          <CardTitle>{t('features.title')}</CardTitle>
          <CardDescription>{t('features.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <ul className='space-y-2 text-sm'>
            <li>✅ {t('features.items.newYork')}</li>
            <li>✅ {t('features.items.typescript')}</li>
            <li>✅ {t('features.items.darkMode')}</li>
            <li>✅ {t('features.items.customizable')}</li>
            <li>✅ {t('features.items.accessibility')}</li>
          </ul>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>{t('techStack.title')}</CardTitle>
          <CardDescription>{t('techStack.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <ul className='space-y-2 text-sm'>
            <li>🔧 {t('techStack.items.radix')}</li>
            <li>🎨 {t('techStack.items.tailwind')}</li>
            <li>⚡ {t('techStack.items.cva')}</li>
            <li>🔀 {t('techStack.items.clsx')}</li>
            <li>🎯 {t('techStack.items.lucide')}</li>
          </ul>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>{t('installation.title')}</CardTitle>
          <CardDescription>{t('installation.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-3'>
            <div className='flex items-center justify-between'>
              <span className='text-sm'>{t('installation.items.shadcn')}</span>
              <span className='rounded bg-green-100 px-2 py-1 text-xs text-green-800 dark:bg-green-900 dark:text-green-100'>
                ✅ {t('installation.status.installed')}
              </span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-sm'>
                {t('installation.items.newYorkStyle')}
              </span>
              <span className='rounded bg-green-100 px-2 py-1 text-xs text-green-800 dark:bg-green-900 dark:text-green-100'>
                ✅ {t('installation.status.configured')}
              </span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-sm'>
                {t('installation.items.baseComponents')}
              </span>
              <span className='rounded bg-green-100 px-2 py-1 text-xs text-green-800 dark:bg-green-900 dark:text-green-100'>
                ✅ {t('installation.status.installed')}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
