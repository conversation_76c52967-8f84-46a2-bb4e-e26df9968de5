'use client';

import {
  G<PERSON><PERSON>,
  Linkedin,
  Mail,
  MapPin,
  Phone,
  Twitter,
  Youtube,
} from 'lucide-react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';

import { ScrollReveal } from '@/components/ui/scroll-reveal';

/**
 * 公司信息栏组件
 */
function CompanySection(): React.JSX.Element {
  const t = useTranslations('footer');

  return (
    <div className='space-y-4'>
      <div className='flex items-center space-x-2'>
        <div className='bg-primary flex h-8 w-8 items-center justify-center rounded-lg transition-all duration-300 hover:scale-110 hover:rotate-12'>
          <span className='text-primary-foreground text-sm font-bold'>T</span>
        </div>
        <span className='text-xl font-bold'>{t('company.name')}</span>
      </div>
      <p className='text-muted-foreground text-sm leading-relaxed'>
        {t('company.description')}
      </p>
      <div className='text-muted-foreground flex items-start space-x-2 text-sm'>
        <MapPin className='mt-0.5 h-4 w-4 flex-shrink-0' />
        <span>{t('company.address')}</span>
      </div>
    </div>
  );
}

/**
 * 产品链接栏组件
 */
function ProductsSection(): React.JSX.Element {
  const t = useTranslations('footer');

  const productLinks = [
    { href: '/products/web-development', label: t('products.webDevelopment') },
    { href: '/products/mobile-apps', label: t('products.mobileApps') },
    { href: '/products/consulting', label: t('products.consulting') },
    { href: '/products/support', label: t('products.support') },
  ];

  return (
    <div className='space-y-4'>
      <h3 className='text-sm font-semibold'>{t('products.title')}</h3>
      <ul className='space-y-3'>
        {productLinks.map((link) => (
          <li key={link.href}>
            <Link
              href={link.href}
              className='text-muted-foreground hover:text-foreground text-sm transition-all duration-200 hover:scale-105'
            >
              {link.label}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
}

/**
 * 支持链接栏组件
 */
function SupportSection(): React.JSX.Element {
  const t = useTranslations('footer');

  const supportLinks = [
    { href: '/docs', label: t('support.documentation') },
    { href: '/help', label: t('support.helpCenter') },
    { href: '/community', label: t('support.community') },
    { href: '/contact', label: t('support.contact') },
  ];

  return (
    <div className='space-y-4'>
      <h3 className='text-sm font-semibold'>{t('support.title')}</h3>
      <ul className='space-y-3'>
        {supportLinks.map((link) => (
          <li key={link.href}>
            <Link
              href={link.href}
              className='text-muted-foreground hover:text-foreground text-sm transition-all duration-200 hover:scale-105'
            >
              {link.label}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
}

/**
 * 联系方式栏组件
 */
function ContactSection(): React.JSX.Element {
  const t = useTranslations('footer');

  const socialLinks = [
    {
      href: 'https://twitter.com/tucsenberg',
      label: t('social.twitter'),
      icon: Twitter,
    },
    {
      href: 'https://linkedin.com/company/tucsenberg',
      label: t('social.linkedin'),
      icon: Linkedin,
    },
    {
      href: 'https://github.com/tucsenberg',
      label: t('social.github'),
      icon: Github,
    },
    {
      href: 'https://youtube.com/@tucsenberg',
      label: t('social.youtube'),
      icon: Youtube,
    },
  ];

  return (
    <div className='space-y-4'>
      <h3 className='text-sm font-semibold'>{t('contact.title')}</h3>
      <div className='space-y-3'>
        <div className='text-muted-foreground flex items-center space-x-2 text-sm'>
          <Mail className='h-4 w-4 flex-shrink-0' />
          <a
            href={`mailto:${t('contact.email')}`}
            className='hover:text-foreground transition-colors'
          >
            {t('contact.email')}
          </a>
        </div>
        <div className='text-muted-foreground flex items-center space-x-2 text-sm'>
          <Phone className='h-4 w-4 flex-shrink-0' />
          <a
            href={`tel:${t('contact.phone')}`}
            className='hover:text-foreground transition-colors'
          >
            {t('contact.phone')}
          </a>
        </div>
      </div>

      {/* 社交媒体图标 */}
      <div className='space-y-3'>
        <h4 className='text-sm font-medium'>{t('social.title')}</h4>
        <div className='flex space-x-3'>
          {socialLinks.map((social) => {
            const IconComponent = social.icon;
            return (
              <a
                key={social.href}
                href={social.href}
                target='_blank'
                rel='noopener noreferrer'
                className='text-muted-foreground hover:text-foreground transition-all duration-200 hover:scale-110 hover:rotate-12'
                aria-label={social.label}
              >
                <IconComponent className='h-5 w-5' />
              </a>
            );
          })}
        </div>
      </div>
    </div>
  );
}

/**
 * 企业级页脚组件
 *
 * 特性：
 * - 四栏布局：公司信息、产品链接、支持链接、联系方式
 * - 响应式设计：桌面端多栏，移动端单栏堆叠
 * - 多语言支持：集成 next-intl
 * - 社交媒体图标：使用 lucide-react
 * - 企业级样式：与导航栏保持一致的设计语言
 */
export function Footer(): React.JSX.Element {
  const t = useTranslations('footer');

  const legalLinks = [
    { href: '/privacy', label: t('legal.privacy') },
    { href: '/terms', label: t('legal.terms') },
    { href: '/cookies', label: t('legal.cookies') },
  ];

  return (
    <footer className='bg-background border-t'>
      <div className='container mx-auto px-4 py-12'>
        {/* 主要内容区域 - 四栏布局 */}
        <div className='grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4'>
          <ScrollReveal animation='slideUp' delay={0}>
            <CompanySection />
          </ScrollReveal>
          <ScrollReveal animation='slideUp' delay={100}>
            <ProductsSection />
          </ScrollReveal>
          <ScrollReveal animation='slideUp' delay={200}>
            <SupportSection />
          </ScrollReveal>
          <ScrollReveal animation='slideUp' delay={300}>
            <ContactSection />
          </ScrollReveal>
        </div>

        {/* 分隔线 */}
        <div className='my-8 border-t' />

        {/* 底部法律信息 */}
        <div className='text-muted-foreground flex flex-col items-center justify-between space-y-4 text-sm md:flex-row md:space-y-0'>
          <p>{t('legal.copyright')}</p>
          <div className='flex space-x-6'>
            {legalLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className='hover:text-foreground transition-colors'
              >
                {link.label}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
}
