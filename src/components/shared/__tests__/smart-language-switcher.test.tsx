/**
 * 智能语言切换器组件测试
 */
import { render, screen } from '@testing-library/react';
import { describe, expect, it, vi } from 'vitest';

import { SmartLanguageSwitcher } from '../smart-language-switcher';

// Mock all dependencies
vi.mock('next-intl', () => ({
  useLocale: () => 'en',
}));

vi.mock('@/i18n/routing', () => ({
  usePathname: () => '/test',
  useRouter: () => ({ replace: vi.fn() }),
}));

vi.mock('@/hooks/use-smart-language-detection', () => ({
  useSmartLanguageDetection: () => ({
    currentLocale: 'en',
    detectionResult: null,
    isDetecting: false,
    switchLanguage: vi.fn(),
    detectAndSwitch: vi.fn(),
    refreshStats: vi.fn(),
    isAutoDetected: false,
    canAutoDetected: true,
    detectionStats: {
      totalDetections: 0,
      sourceDistribution: {},
      localeDistribution: {},
    },
  }),
}));

interface MockProps {
  children: React.ReactNode;
  [key: string]: unknown;
}

vi.mock('@/components/ui/button', () => ({
  Button: ({ children, ...props }: MockProps) => (
    <button {...props}>{children}</button>
  ),
}));

vi.mock('@/components/ui/dropdown-menu', () => ({
  DropdownMenu: ({ children }: MockProps) => <div>{children}</div>,
  DropdownMenuContent: ({ children }: MockProps) => <div>{children}</div>,
  DropdownMenuItem: ({ children }: MockProps) => <div>{children}</div>,
  DropdownMenuSeparator: () => <div />,
  DropdownMenuTrigger: ({ children }: MockProps) => <div>{children}</div>,
  DropdownMenuLabel: ({ children }: MockProps) => <div>{children}</div>,
}));

vi.mock('@/components/ui/badge', () => ({
  Badge: ({ children }: MockProps) => <span>{children}</span>,
}));

vi.mock('@/components/ui/tooltip', () => ({
  TooltipProvider: ({ children }: MockProps) => <div>{children}</div>,
  Tooltip: ({ children }: MockProps) => <div>{children}</div>,
  TooltipTrigger: ({ children }: MockProps) => <div>{children}</div>,
  TooltipContent: ({ children }: MockProps) => <div>{children}</div>,
}));

vi.mock('lucide-react', () => ({
  Languages: () => <span>Languages</span>,
  Zap: () => <span>Zap</span>,
  BarChart3: () => <span>Chart</span>,
  Info: () => <span>Info</span>,
}));

describe('SmartLanguageSwitcher', () => {
  it('应该正确渲染', () => {
    render(<SmartLanguageSwitcher />);
    expect(screen.getByRole('button')).toBeInTheDocument();
  });
});
