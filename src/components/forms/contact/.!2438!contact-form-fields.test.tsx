import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { describe, expect, it, vi } from 'vitest';
import { Form } from '@/components/ui/form';
import { type ContactFormData, contactFormSchema } from '@/lib/validations';
import { fireEvent, render, screen, waitFor } from '@/test/utils';
import {
  ContactFormFields,
  OptionalFields,
  RequiredFields,
} from './contact-form-fields';
import { type ContactFormFieldsProps } from './contact-form-types';

