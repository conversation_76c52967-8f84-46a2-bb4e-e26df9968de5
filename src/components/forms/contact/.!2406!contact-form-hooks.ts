import { use<PERSON>allback, useMemo, useState } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { useFormSubmission } from '@/hooks/use-form-submission';
import { type ContactFormData, contactFormSchema } from '@/lib/validations';
import {
  type ContactFormLogicReturn,
  type FormStatus,
} from './contact-form-types';

/**
 * 联系表单逻辑Hook
 *
