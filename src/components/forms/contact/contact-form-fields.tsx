import { type ReactElement, memo } from 'react';

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

import { type ContactFormFieldsProps } from './contact-form-types';

/**
 * 必填字段组件
 */
export const RequiredFields = memo(
  ({ form, t }: ContactFormFieldsProps): ReactElement => {
    return (
      <>
        {/* 必填项：姓名 */}
        <FormField
          control={form.control}
          name='name'
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel>
                {t('fields.name.label')}
                <span className='text-red-500' aria-label='必填字段'>
                  *
                </span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={t('fields.name.placeholder')}
                  aria-describedby={
                    fieldState.error !== undefined ? `name-error` : undefined
                  }
                  aria-invalid={fieldState.error !== undefined}
                  aria-required='true'
                  {...field}
                />
              </FormControl>
              <FormMessage id='name-error' role='alert' />
            </FormItem>
          )}
        />

        {/* 必填项：邮箱 */}
        <FormField
          control={form.control}
          name='email'
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel>
                {t('fields.email.label')}
                <span className='text-red-500' aria-label='必填字段'>
                  *
                </span>
              </FormLabel>
              <FormControl>
                <Input
                  type='email'
                  placeholder={t('fields.email.placeholder')}
                  autoComplete='email'
                  aria-describedby={
                    fieldState.error !== undefined ? `email-error` : undefined
                  }
                  aria-invalid={fieldState.error !== undefined}
                  aria-required='true'
                  {...field}
                />
              </FormControl>
              <FormMessage id='email-error' role='alert' />
            </FormItem>
          )}
        />
      </>
    );
  }
);
RequiredFields.displayName = 'RequiredFields';

/**
 * 沟通事项字段组件
 */
const PurposeField = memo(
  ({ form, t }: ContactFormFieldsProps): ReactElement => (
    <FormField
      control={form.control}
      name='purpose'
      render={({ field, fieldState }) => (
        <FormItem>
          <FormLabel>{t('fields.purpose.label')}</FormLabel>
          <Select
            onValueChange={field.onChange}
            defaultValue={field.value ?? ''}
            aria-describedby={
              fieldState.error !== undefined ? `purpose-error` : undefined
            }
            aria-invalid={fieldState.error !== undefined}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder={t('fields.purpose.placeholder')} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              <SelectItem value='business'>
                {t('fields.purpose.options.business')}
              </SelectItem>
              <SelectItem value='collaboration'>
                {t('fields.purpose.options.collaboration')}
              </SelectItem>
              <SelectItem value='inquiry'>
                {t('fields.purpose.options.inquiry')}
              </SelectItem>
              <SelectItem value='other'>
                {t('fields.purpose.options.other')}
              </SelectItem>
            </SelectContent>
          </Select>
          <FormMessage id='purpose-error' role='alert' />
        </FormItem>
      )}
    />
  )
);
PurposeField.displayName = 'PurposeField';

/**
 * 地区字段组件
 */
const RegionField = memo(
  ({ form, t }: ContactFormFieldsProps): ReactElement => (
    <FormField
      control={form.control}
      name='region'
      render={({ field, fieldState }) => (
        <FormItem>
          <FormLabel>{t('fields.region.label')}</FormLabel>
          <Select
            onValueChange={field.onChange}
            defaultValue={field.value ?? ''}
            aria-describedby={
              fieldState.error !== undefined ? `region-error` : undefined
            }
            aria-invalid={fieldState.error !== undefined}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder={t('fields.region.placeholder')} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              <SelectItem value='asia-pacific'>
                {t('fields.region.options.asia-pacific')}
              </SelectItem>
              <SelectItem value='europe'>
                {t('fields.region.options.europe')}
              </SelectItem>
              <SelectItem value='north-america'>
                {t('fields.region.options.north-america')}
              </SelectItem>
              <SelectItem value='other'>
                {t('fields.region.options.other')}
              </SelectItem>
            </SelectContent>
          </Select>
          <FormMessage id='region-error' role='alert' />
        </FormItem>
      )}
    />
  )
);
RegionField.displayName = 'RegionField';

/**
 * 消息字段组件
 */
const MessageField = memo(
  ({ form, t }: ContactFormFieldsProps): ReactElement => (
    <FormField
      control={form.control}
      name='message'
      render={({ field, fieldState }) => (
        <FormItem>
          <FormLabel>{t('fields.message.label')}</FormLabel>
          <FormControl>
            <Textarea
              placeholder={t('fields.message.placeholder')}
              className='min-h-[120px] resize-none'
              aria-describedby={
                fieldState.error !== undefined ? `message-error` : undefined
              }
              aria-invalid={fieldState.error !== undefined}
              {...field}
            />
          </FormControl>
          <FormMessage id='message-error' role='alert' />
        </FormItem>
      )}
    />
  )
);
MessageField.displayName = 'MessageField';

/**
 * 可选字段组件
 */
export const OptionalFields = memo(
  ({ form, t }: ContactFormFieldsProps): ReactElement => (
    <>
      <PurposeField form={form} t={t} />
      <RegionField form={form} t={t} />
      <MessageField form={form} t={t} />
    </>
  )
);
OptionalFields.displayName = 'OptionalFields';

/**
 * 联系表单字段组合组件
 */
export const ContactFormFields = memo(
  ({ form, t }: ContactFormFieldsProps): ReactElement => {
    return (
      <>
        <RequiredFields form={form} t={t} />
        <OptionalFields form={form} t={t} />
      </>
    );
  }
);
ContactFormFields.displayName = 'ContactFormFields';
