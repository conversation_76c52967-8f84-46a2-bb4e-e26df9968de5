import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { describe, expect, it, vi } from 'vitest';

import { Form } from '@/components/ui/form';
import { type ContactFormData, contactFormSchema } from '@/lib/validations';
import { fireEvent, render, screen, waitFor } from '@/test/utils';

import {
  ContactFormFields,
  OptionalFields,
  RequiredFields,
} from './contact-form-fields';
import { type ContactFormFieldsProps } from './contact-form-types';

// 模拟翻译函数
const mockT = vi.fn((key: string) => {
  const translations = new Map([
    ['fields.name.label', '姓名'],
    ['fields.name.placeholder', '请输入您的姓名'],
    ['fields.email.label', '邮箱'],
    ['fields.email.placeholder', '请输入您的邮箱'],
    ['fields.purpose.label', '沟通事项'],
    ['fields.purpose.placeholder', '请选择沟通事项'],
    ['fields.purpose.options.business', '商务合作'],
    ['fields.purpose.options.collaboration', '技术合作'],
    ['fields.purpose.options.inquiry', '咨询问题'],
    ['fields.purpose.options.other', '其他'],
    ['fields.region.label', '地区'],
    ['fields.region.placeholder', '请选择您的地区'],
    ['fields.region.options.asia-pacific', '亚太地区'],
    ['fields.region.options.europe', '欧洲'],
    ['fields.region.options.north-america', '北美'],
    ['fields.region.options.other', '其他'],
    ['fields.message.label', '附加信息'],
    ['fields.message.placeholder', '请输入附加信息（可选）'],
  ]);
  return translations.get(key) ?? key;
});

// 测试包装器组件
// 创建测试组件的工厂函数
const createTestComponent = (
  Component: React.ComponentType<ContactFormFieldsProps>
) => {
  return function TestComponent() {
    const form = useForm<ContactFormData>({
      resolver: zodResolver(contactFormSchema),
      defaultValues: {
        name: '',
        email: '',
        purpose: '',
        region: '',
        message: '',
      },
    });

    return (
      <Form {...form}>
        <Component form={form} t={mockT} />
      </Form>
    );
  };
};

describe('ContactFormFields 组件', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('RequiredFields 组件', () => {
    it('应该渲染必填字段（姓名和邮箱）', () => {
      const TestComponent = createTestComponent(RequiredFields);
      render(<TestComponent />);

      // 检查姓名字段
      expect(screen.getByLabelText(/姓名/)).toBeInTheDocument();
      expect(screen.getByPlaceholderText('请输入您的姓名')).toBeInTheDocument();

      // 检查邮箱字段
      expect(screen.getByLabelText(/邮箱/)).toBeInTheDocument();
      expect(screen.getByPlaceholderText('请输入您的邮箱')).toBeInTheDocument();

      // 检查必填标识（应该有两个：姓名和邮箱）
      const requiredIndicators = screen.getAllByLabelText('必填字段');
      expect(requiredIndicators).toHaveLength(2);
    });

    it('应该正确设置可访问性属性', () => {
      const TestComponent = createTestComponent(RequiredFields);
      render(<TestComponent />);

      const nameInput = screen.getByPlaceholderText('请输入您的姓名');
      const emailInput = screen.getByPlaceholderText('请输入您的邮箱');

      // 检查 ARIA 属性
      expect(nameInput).toHaveAttribute('aria-required', 'true');
      expect(emailInput).toHaveAttribute('aria-required', 'true');
      expect(emailInput).toHaveAttribute('type', 'email');
    });

    it('应该支持用户输入', async () => {
      const TestComponent = createTestComponent(RequiredFields);
      render(<TestComponent />);

      const nameInput = screen.getByPlaceholderText('请输入您的姓名');
      const emailInput = screen.getByPlaceholderText('请输入您的邮箱');

      // 测试输入
      fireEvent.change(nameInput, { target: { value: '张三' } });
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });

      await waitFor(() => {
        expect(nameInput).toHaveValue('张三');
        expect(emailInput).toHaveValue('<EMAIL>');
      });
    });
  });

  describe('OptionalFields 组件', () => {
    it('应该渲染可选字段', () => {
      const TestComponent = createTestComponent(OptionalFields);
      render(<TestComponent />);

      // 检查沟通事项字段
      expect(screen.getByLabelText(/沟通事项/)).toBeInTheDocument();
      expect(screen.getByText('请选择沟通事项')).toBeInTheDocument();

      // 检查地区字段
      expect(screen.getByLabelText(/地区/)).toBeInTheDocument();
      expect(screen.getByText('请选择您的地区')).toBeInTheDocument();

      // 检查附加信息字段
      expect(screen.getByLabelText(/附加信息/)).toBeInTheDocument();
      expect(
        screen.getByPlaceholderText('请输入附加信息（可选）')
      ).toBeInTheDocument();
    });

    it('应该支持选择下拉选项', async () => {
      const TestComponent = createTestComponent(OptionalFields);
      render(<TestComponent />);

      // 测试沟通事项选择
      const purposeTrigger = screen.getByText('请选择沟通事项');
      fireEvent.click(purposeTrigger);

      await waitFor(() => {
        expect(screen.getByText('商务合作')).toBeInTheDocument();
        expect(screen.getByText('技术合作')).toBeInTheDocument();
        expect(screen.getByText('咨询问题')).toBeInTheDocument();
      });
    });
  });

  describe('ContactFormFields 组件', () => {
    it('应该渲染完整的表单字段', () => {
      const TestComponent = createTestComponent(ContactFormFields);
      render(<TestComponent />);

      // 验证所有字段都存在
      expect(screen.getByLabelText(/姓名/)).toBeInTheDocument();
      expect(screen.getByLabelText(/邮箱/)).toBeInTheDocument();
      expect(screen.getByLabelText(/沟通事项/)).toBeInTheDocument();
      expect(screen.getByLabelText(/地区/)).toBeInTheDocument();
      expect(screen.getByLabelText(/附加信息/)).toBeInTheDocument();
    });

    it('应该正确调用翻译函数', () => {
      const TestComponent = createTestComponent(ContactFormFields);
      render(<TestComponent />);

      // 验证翻译函数被正确调用
      expect(mockT).toHaveBeenCalledWith('fields.name.label');
      expect(mockT).toHaveBeenCalledWith('fields.email.label');
      expect(mockT).toHaveBeenCalledWith('fields.purpose.label');
      expect(mockT).toHaveBeenCalledWith('fields.region.label');
      expect(mockT).toHaveBeenCalledWith('fields.message.label');
    });
  });
});
