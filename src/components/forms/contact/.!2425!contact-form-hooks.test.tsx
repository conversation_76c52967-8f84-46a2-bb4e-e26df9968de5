import { act, renderHook } from '@testing-library/react';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
  useContactFormConfig,
  useContactFormLogic,
} from './contact-form-hooks';

// 模拟 useFormSubmission Hook
const mockSubmitWithErrorHandling = vi.fn();
const mockRetrySubmission = vi.fn();
const mockClearError = vi.fn();

vi.mock('@/hooks/use-form-submission', () => ({
  useFormSubmission: vi.fn(() => ({
    isSubmitting: false,
    error: null,
    canRetry: false,
    submitWithErrorHandling: mockSubmitWithErrorHandling,
    retrySubmission: mockRetrySubmission,
    clearError: mockClearError,
  })),
}));

