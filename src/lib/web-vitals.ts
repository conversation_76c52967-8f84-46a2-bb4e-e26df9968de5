/**
 * Web Vitals 性能指标收集工具
 * 企业级性能监控 - 实时收集和分析Core Web Vitals指标
 */
import { onCLS, onFCP, onINP, onLCP, onTTFB } from 'web-vitals';

// 性能指标类型定义
export interface WebVitalMetric {
  id: string;
  name: string;
  value: number;
  rating: 'good' | 'needs-improvement' | 'poor';
  delta: number;
  entries: PerformanceEntry[];
  navigationType: string;
  timestamp: number;
  url: string;
  userAgent: string;
}

// 性能阈值配置 (基于Google推荐标准)
export const PERFORMANCE_THRESHOLDS = {
  // Largest Contentful Paint (最大内容绘制)
  LCP: {
    good: 2500, // ≤ 2.5s
    poor: 4000, // > 4.0s
  },
  // Interaction to Next Paint (交互到下次绘制) - 替代FID
  INP: {
    good: 200, // ≤ 200ms
    poor: 500, // > 500ms
  },
  // Cumulative Layout Shift (累积布局偏移)
  CLS: {
    good: 0.1, // ≤ 0.1
    poor: 0.25, // > 0.25
  },
  // First Contentful Paint (首次内容绘制)
  FCP: {
    good: 1800, // ≤ 1.8s
    poor: 3000, // > 3.0s
  },
  // Time to First Byte (首字节时间)
  TTFB: {
    good: 800, // ≤ 800ms
    poor: 1800, // > 1.8s
  },
} as const;

// 性能评级函数
function getRating(
  name: string,
  value: number
): 'good' | 'needs-improvement' | 'poor' {
  const thresholds =
    PERFORMANCE_THRESHOLDS[name as keyof typeof PERFORMANCE_THRESHOLDS];
  if (thresholds === undefined) return 'good';

  if (value <= thresholds.good) return 'good';
  if (value <= thresholds.poor) return 'needs-improvement';
  return 'poor';
}

// Web Vitals 收集器接口
export interface IWebVitalsCollector {
  startCollection(): void;
  onMetric(callback: (metric: WebVitalMetric) => void): void;
  getAllMetrics(): WebVitalMetric[];
  getMetric(name: string): WebVitalMetric | undefined;
  getPerformanceSummary(): {
    score: number;
    rating: 'good' | 'needs-improvement' | 'poor';
    metrics: Record<string, { value: number; rating: string }>;
  };
  exportReport(): string;
}

// 增强指标数据
function enhanceMetric(metric: {
  id: string;
  name: string;
  value: number;
  delta: number;
  entries: PerformanceEntry[];
  navigationType: string;
}): WebVitalMetric {
  return {
    ...metric,
    rating: getRating(metric.name, metric.value),
    timestamp: Date.now(),
    url: window.location.href,
    userAgent: navigator.userAgent,
  };
}

// 性能数据收集器类
export class WebVitalsCollector implements IWebVitalsCollector {
  private readonly metrics: Map<string, WebVitalMetric> = new Map();
  private readonly callbacks: Array<(metric: WebVitalMetric) => void> = [];
  private isCollecting = false;

  constructor() {
    this.startCollection();
  }

  // 开始收集性能指标
  startCollection(): void {
    if (this.isCollecting) return;
    this.isCollecting = true;

    // 收集 LCP (Largest Contentful Paint)
    onLCP((metric) => {
      const enhancedMetric = enhanceMetric(metric);
      this.handleMetric(enhancedMetric);
    });

    // 收集 INP (Interaction to Next Paint)
    onINP((metric) => {
      const enhancedMetric = enhanceMetric(metric);
      this.handleMetric(enhancedMetric);
    });

    // 收集 CLS (Cumulative Layout Shift)
    onCLS((metric) => {
      const enhancedMetric = enhanceMetric(metric);
      this.handleMetric(enhancedMetric);
    });

    // 收集 FCP (First Contentful Paint)
    onFCP((metric) => {
      const enhancedMetric = enhanceMetric(metric);
      this.handleMetric(enhancedMetric);
    });

    // 收集 TTFB (Time to First Byte)
    onTTFB((metric) => {
      const enhancedMetric = enhanceMetric(metric);
      this.handleMetric(enhancedMetric);
    });

     
    if (process.env.NODE_ENV === 'development') {
      if (process.env.NODE_ENV === 'development') {
        console.log('🚀 Web Vitals 性能监控已启动');
      }
    }
  }

  // 处理收集到的指标
  private handleMetric(metric: WebVitalMetric): void {
    this.metrics.set(metric.name, metric);

    // 控制台输出 (开发环境)
    if (process.env.NODE_ENV === 'development') {
       
      if (process.env.NODE_ENV === 'development') {
        console.log(`📊 ${metric.name}:`, {
        value: metric.value,
        rating: metric.rating,
        delta: metric.delta,
      });
      }
    }

    // 触发回调函数
    this.callbacks.forEach((callback) => callback(metric));

    // 发送到分析服务
    this.sendToAnalytics(metric);
  }

  // 添加回调函数
  onMetric(callback: (metric: WebVitalMetric) => void): void {
    this.callbacks.push(callback);
  }

  // 获取所有指标
  getAllMetrics(): WebVitalMetric[] {
    return [...this.metrics.values()];
  }

  // 获取特定指标
  getMetric(name: string): WebVitalMetric | undefined {
    return this.metrics.get(name);
  }

  // 获取性能摘要
  getPerformanceSummary(): {
    score: number;
    rating: 'good' | 'needs-improvement' | 'poor';
    metrics: Record<string, { value: number; rating: string }>;
  } {
    const metrics = this.getAllMetrics();
    const scores = metrics.map((metric) => {
      switch (metric.rating) {
        case 'good':
          return 100;
        case 'needs-improvement':
          return 50;
        case 'poor':
          return 0;
        default:
          return 0;
      }
    });

    const averageScore =
      scores.length > 0
        ? scores.reduce((a: number, b: number) => a + b, 0) / scores.length
        : 0;

    let overallRating: 'good' | 'needs-improvement' | 'poor' = 'good';
    if (averageScore < 50) overallRating = 'poor';
    else if (averageScore < 80) overallRating = 'needs-improvement';

    const metricsData = metrics.reduce(
      (acc, metric) => {
        acc[metric.name] = {
          value: metric.value,
          rating: metric.rating,
        };
        return acc;
      },
      {} as Record<string, { value: number; rating: string }>
    );

    return {
      score: Math.round(averageScore),
      rating: overallRating,
      metrics: metricsData,
    };
  }

  // 发送到分析服务
  private sendToAnalytics(metric: WebVitalMetric): void {
    // 这里可以集成各种分析服务
    // 例如: Google Analytics, Vercel Analytics, 自定义分析服务等

    // Google Analytics 4 示例
    const windowWithGtag = window as {
      gtag?: (
        event: string,
        name: string,
        params: Record<string, unknown>
      ) => void;
    };
    if (
      typeof windowWithGtag.gtag !== 'undefined' &&
      windowWithGtag.gtag !== undefined
    ) {
      windowWithGtag.gtag('event', metric.name, {
        event_category: 'Web Vitals',
        value: Math.round(metric.value),
        metric_rating: metric.rating,
        custom_parameter_1: metric.id,
      });
    }

    // 自定义分析服务示例
    if (process.env.NODE_ENV === 'production') {
      fetch('/api/analytics/web-vitals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          metric: metric.name,
          value: metric.value,
          rating: metric.rating,
          url: metric.url,
          timestamp: metric.timestamp,
          userAgent: metric.userAgent,
        }),
      }).catch((error) => {
         
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
            console.warn('Failed to send web vitals to analytics:', error);
          }
        }
      });
    }
  }

  // 导出性能报告
  exportReport(): string {
    const summary = this.getPerformanceSummary();
    const metrics = this.getAllMetrics();

    const report = {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      summary,
      metrics: metrics.map((metric) => ({
        name: metric.name,
        value: metric.value,
        rating: metric.rating,
        delta: metric.delta,
        id: metric.id,
      })),
    };

    return JSON.stringify(report, null, 2);
  }
}

// 全局实例
let webVitalsCollector: IWebVitalsCollector | null = null;

// 初始化Web Vitals收集器
export function initWebVitals(): IWebVitalsCollector {
  if (typeof window === 'undefined') {
    // 服务端渲染环境，返回空实现
    return {
      startCollection: () => {},
      onMetric: () => {},
      getAllMetrics: () => [],
      getMetric: () => undefined,
      getPerformanceSummary: () => ({
        score: 0,
        rating: 'good' as const,
        metrics: {},
      }),
      exportReport: () => '{}',
    } as IWebVitalsCollector;
  }

  webVitalsCollector ??= new WebVitalsCollector();

  return webVitalsCollector;
}

// 便捷函数：获取当前性能指标
export async function getCurrentWebVitals(): Promise<{
  LCP?: number;
  INP?: number;
  CLS?: number;
  FCP?: number;
  TTFB?: number;
}> {
  return new Promise((resolve) => {
    const metrics: Record<string, number> = {};
    let collected = 0;
    const expectedMetrics = 5;

    const collectMetric = (
      name: string,
      onMetric: (callback: (metric: { value: number }) => void) => void
    ): void => {
      onMetric((metric) => {
        // eslint-disable-next-line security/detect-object-injection
        metrics[name] = metric.value;
        collected++;
        if (collected >= expectedMetrics) {
          resolve(metrics);
        }
      });
    };

    // 收集各项指标
    collectMetric('LCP', onLCP);
    collectMetric('INP', onINP);
    collectMetric('CLS', onCLS);
    collectMetric('FCP', onFCP);
    collectMetric('TTFB', onTTFB);

    // 超时保护
    setTimeout(() => resolve(metrics), 3000);
  });
}

// 导出默认实例
export default initWebVitals;
