/**
 * 主题性能优化管理器
 *
 * 功能特性：
 * - 主题文件预加载
 * - 缓存策略优化
 * - 智能加载策略
 * - 加载状态管理
 * - 性能监控
 * - 内存管理
 * - 懒加载机制
 */

// 性能配置接口
export interface PerformanceConfig {
  enablePreloading: boolean;
  enableCaching: boolean;
  enableLazyLoading: boolean;
  cacheSize: number; // MB
  preloadThemes: string[];
  cacheTTL: number; // 缓存生存时间 (毫秒)
  performanceMonitoring: boolean;
  memoryThreshold: number; // 内存阈值 (MB)
  networkOptimization: boolean;
  compressionEnabled: boolean;
}

// 加载状态
export interface LoadingState {
  isLoading: boolean;
  loadedThemes: Set<string>;
  failedThemes: Set<string>;
  loadingProgress: number;
  currentTheme: string | null;
  preloadedThemes: Set<string>;
}

// 性能指标
export interface PerformanceMetrics {
  loadTime: number;
  cacheHitRate: number;
  memoryUsage: number;
  networkRequests: number;
  compressionRatio: number;
  errorRate: number;
  averageLoadTime: number;
  peakMemoryUsage: number;
  totalThemesLoaded: number;
  cacheSize: number;
}

// 缓存项
export interface CacheItem {
  data: unknown;
  timestamp: number;
  size: number;
  accessCount: number;
  lastAccessed: number;
  compressed: boolean;
}

// 性能事件类型
export type PerformanceEventType =
  | 'theme-load-start'
  | 'theme-load-complete'
  | 'theme-load-error'
  | 'cache-hit'
  | 'cache-miss'
  | 'memory-warning'
  | 'performance-update';

export interface PerformanceEvent {
  type: PerformanceEventType;
  theme?: string;
  metrics: PerformanceMetrics;
  timestamp: number;
  details?: unknown;
}

/**
 * 主题性能优化管理器核心类
 */
export class ThemePerformanceManager extends EventTarget {
  private config: PerformanceConfig;
  private readonly loadingState: LoadingState;
  private readonly cache: Map<string, CacheItem> = new Map();
  private metrics: PerformanceMetrics;
  private loadTimes: number[] = [];
  private isInitialized = false;
  private readonly preloadPromises: Map<string, Promise<any>> = new Map();
  private compressionWorker: Worker | null = null;

  constructor(initialConfig?: Partial<PerformanceConfig>) {
    super();

    // 默认配置
    this.config = {
      enablePreloading: true,
      enableCaching: true,
      enableLazyLoading: true,
      cacheSize: 50, // 50MB
      preloadThemes: ['light', 'dark'],
      cacheTTL: 24 * 60 * 60 * 1000, // 24小时
      performanceMonitoring: true,
      memoryThreshold: 100, // 100MB
      networkOptimization: true,
      compressionEnabled: true,
      ...initialConfig,
    };

    // 初始化加载状态
    this.loadingState = {
      isLoading: false,
      loadedThemes: new Set(),
      failedThemes: new Set(),
      loadingProgress: 0,
      currentTheme: null,
      preloadedThemes: new Set(),
    };

    // 初始化性能指标
    this.metrics = {
      loadTime: 0,
      cacheHitRate: 0,
      memoryUsage: 0,
      networkRequests: 0,
      compressionRatio: 0,
      errorRate: 0,
      averageLoadTime: 0,
      peakMemoryUsage: 0,
      totalThemesLoaded: 0,
      cacheSize: 0,
    };

    this.initialize();
  }

  /**
   * 初始化管理器
   */
  private initialize(): void {
    if (typeof window === 'undefined') {
      return;
    }

    this.setupCompressionWorker();
    this.setupMemoryMonitoring();
    this.setupPerformanceObserver();

    if (this.config.enablePreloading) {
      this.preloadThemes();
    }

    this.isInitialized = true;
    this.emitEvent('performance-update');
  }

  /**
   * 设置压缩 Worker
   */
  private setupCompressionWorker(): void {
    if (!this.config.compressionEnabled || typeof Worker === 'undefined') {
      return;
    }

    try {
      // 创建内联 Worker 用于数据压缩
      const workerScript = `
        self.onmessage = function(e) {
          const { data, id } = e.data;
          try {
            // 简单的 JSON 压缩（实际项目中可使用 LZ4 等算法）
            const compressed = JSON.stringify(data);
            const original = JSON.stringify(data, null, 2);
            const ratio = compressed.length / original.length;
            
            self.postMessage({
              id,
              compressed: compressed,
              ratio: ratio,
              success: true
            });
          } catch (error) {
            self.postMessage({
              id,
              error: error.message,
              success: false
            });
          }
        };
      `;

      const blob = new Blob([workerScript], { type: 'application/javascript' });
      this.compressionWorker = new Worker(URL.createObjectURL(blob));
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
            console.warn('Failed to create compression worker:', error);
          }
        }
      }
    }
  }

  /**
   * 设置内存监控
   */
  private setupMemoryMonitoring(): void {
    if (typeof window === 'undefined' || !this.config.performanceMonitoring) {
      return;
    }

    // 监控内存使用情况
    const checkMemory = () : void => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;

        this.metrics.memoryUsage = usedMB;
        this.metrics.peakMemoryUsage = Math.max(
          this.metrics.peakMemoryUsage,
          usedMB
        );

        if (usedMB > this.config.memoryThreshold) {
          this.emitEvent('memory-warning', undefined, { memoryUsage: usedMB });
          this.cleanupCache();
        }
      }
    };

    // 每30秒检查一次内存
    setInterval(checkMemory, 30000);
    checkMemory(); // 立即检查一次
  }

  /**
   * 设置性能观察器
   */
  private setupPerformanceObserver(): void {
    if (typeof window === 'undefined' || !this.config.performanceMonitoring) {
      return;
    }

    try {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry) => {
            if (entry.name.includes('theme')) {
              this.updateLoadTimeMetrics(entry.duration);
            }
          });
        });

        observer.observe({ entryTypes: ['measure', 'navigation'] });
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
            console.warn('Failed to setup performance observer:', error);
          }
        }
      }
    }
  }

  /**
   * 预加载主题
   */
  private async preloadThemes(): Promise<void> {
    const { preloadThemes } = this.config;

    for (const theme of preloadThemes) {
      if (!this.loadingState.preloadedThemes.has(theme)) {
        const promise = this.loadTheme(theme, true);
        this.preloadPromises.set(theme, promise);

        try {
          await promise;
          this.loadingState.preloadedThemes.add(theme);
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            if (process.env.NODE_ENV === 'development') {
              if (process.env.NODE_ENV === 'development') {
                console.warn(`Failed to preload theme ${theme}:`, error);
              }
            }
          }
        }
      }
    }
  }

  /**
   * 加载主题
   */
  async loadTheme(themeName: string, isPreload = false): Promise<any> {
    const startTime = performance.now();

    if (!isPreload) {
      this.loadingState.isLoading = true;
      this.loadingState.currentTheme = themeName;
      this.emitEvent('theme-load-start', themeName);
    }

    try {
      // 检查缓存
      const cached = this.getCachedTheme(themeName);
      if (cached) {
        this.updateMetrics('cache-hit');
        if (!isPreload) {
          this.loadingState.isLoading = false;
          this.emitEvent('theme-load-complete', themeName);
        }
        return cached;
      }

      // 从网络加载
      this.updateMetrics('cache-miss');
      const themeData = await this.fetchThemeData(themeName);

      // 缓存主题数据
      if (this.config.enableCaching) {
        await this.cacheTheme(themeName, themeData);
      }

      // 更新状态
      this.loadingState.loadedThemes.add(themeName);
      this.loadingState.failedThemes.delete(themeName);

      const loadTime = performance.now() - startTime;
      this.updateLoadTimeMetrics(loadTime);

      if (!isPreload) {
        this.loadingState.isLoading = false;
        this.emitEvent('theme-load-complete', themeName);
      }

      return themeData;
    } catch (error) {
      this.loadingState.failedThemes.add(themeName);
      this.loadingState.loadedThemes.delete(themeName);

      if (!isPreload) {
        this.loadingState.isLoading = false;
        this.emitEvent('theme-load-error', themeName, { error });
      }

      throw error;
    }
  }

  /**
   * 从网络获取主题数据
   */
  private async fetchThemeData(themeName: string): Promise<any> {
    this.metrics.networkRequests++;

    const response = await fetch(`/api/themes/${themeName}`);
    if (!response.ok) {
      throw new Error(
        `Failed to fetch theme ${themeName}: ${response.statusText}`
      );
    }

    return response.json();
  }

  /**
   * 获取缓存的主题
   */
  private getCachedTheme(themeName: string): unknown | null {
    const cached = this.cache.get(themeName);

    if (!cached) {
      return null;
    }

    // 检查缓存是否过期
    const now = Date.now();
    if (now - cached.timestamp > this.config.cacheTTL) {
      this.cache.delete(themeName);
      return null;
    }

    // 更新访问信息
    cached.accessCount++;
    cached.lastAccessed = now;

    return cached.data;
  }

  /**
   * 缓存主题数据
   */
  private async cacheTheme(themeName: string, data: unknown): Promise<void> {
    const timestamp = Date.now();
    let compressed = false;
    let finalData = data;

    // 压缩数据（如果启用）
    if (this.config.compressionEnabled && this.compressionWorker) {
      try {
        finalData = await this.compressData(data);
        compressed = true;
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          if (process.env.NODE_ENV === 'development') {
            if (process.env.NODE_ENV === 'development') {
              console.warn('Failed to compress theme data:', error);
            }
          }
        }
      }
    }

    const size = this.estimateSize(finalData);

    const cacheItem: CacheItem = {
      data: finalData,
      timestamp,
      size,
      accessCount: 1,
      lastAccessed: timestamp,
      compressed,
    };

    // 检查缓存大小限制
    await this.ensureCacheSpace(size);

    this.cache.set(themeName, cacheItem);
    this.updateCacheMetrics();
  }

  /**
   * 压缩数据
   */
  private compressData(data: unknown): Promise<unknown> {
    return new Promise((resolve, reject) => {
      if (!this.compressionWorker) {
        resolve(data);
        return;
      }

      const id = Math.random().toString(36).substr(2, 9);

      const handleMessage = (e: MessageEvent) => {
        if (e.data.id === id) {
          this.compressionWorker!.removeEventListener('message', handleMessage);

          if (e.data.success) {
            this.metrics.compressionRatio = e.data.ratio;
            resolve(e.data.compressed);
          } else {
            reject(new Error(e.data.error));
          }
        }
      };

      this.compressionWorker.addEventListener('message', handleMessage);
      this.compressionWorker.postMessage({ data, id });
    });
  }

  /**
   * 估算数据大小
   */
  private estimateSize(data: unknown): number {
    try {
      return new Blob([JSON.stringify(data)]).size;
    } catch {
      return JSON.stringify(data).length * 2; // 粗略估算
    }
  }

  /**
   * 确保缓存空间
   */
  private async ensureCacheSpace(requiredSize: number): Promise<void> {
    const maxSizeBytes = this.config.cacheSize * 1024 * 1024;
    let currentSize = this.getCurrentCacheSize();

    if (currentSize + requiredSize <= maxSizeBytes) {
      return;
    }

    // 使用 LRU 策略清理缓存
    const entries = [...this.cache.entries()].sort(
      ([, a], [, b]) => a.lastAccessed - b.lastAccessed
    );

    for (const [key, item] of entries) {
      this.cache.delete(key);
      currentSize -= item.size;

      if (currentSize + requiredSize <= maxSizeBytes) {
        break;
      }
    }
  }

  /**
   * 获取当前缓存大小
   */
  private getCurrentCacheSize(): number {
    let size = 0;
    for (const item of this.cache.values()) {
      size += item.size;
    }
    return size;
  }

  /**
   * 清理缓存
   */
  private cleanupCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > this.config.cacheTTL) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach((key) => this.cache.delete(key));
    this.updateCacheMetrics();
  }

  /**
   * 更新加载时间指标
   */
  private updateLoadTimeMetrics(loadTime: number): void {
    this.loadTimes.push(loadTime);

    // 保持最近100次加载时间
    if (this.loadTimes.length > 100) {
      this.loadTimes.shift();
    }

    this.metrics.loadTime = loadTime;
    this.metrics.averageLoadTime =
      this.loadTimes.reduce((a, b) => a + b, 0) / this.loadTimes.length;
    this.metrics.totalThemesLoaded++;
  }

  /**
   * 更新缓存指标
   */
  private updateCacheMetrics(): void {
    this.metrics.cacheSize = this.getCurrentCacheSize() / 1024 / 1024; // MB

    // 计算缓存命中率
    const totalRequests = this.metrics.networkRequests + this.cache.size;
    this.metrics.cacheHitRate =
      totalRequests > 0 ? (this.cache.size / totalRequests) * 100 : 0;
  }

  /**
   * 更新指标
   */
  private updateMetrics(type: 'cache-hit' | 'cache-miss'): void {
    if (type === 'cache-hit') {
      // 缓存命中在 getCachedTheme 中处理
    } else if (type === 'cache-miss') {
      // 缓存未命中在 loadTheme 中处理
    }

    this.updateCacheMetrics();
  }

  /**
   * 发出事件
   */
  private emitEvent(
    type: PerformanceEventType,
    theme?: string,
    details?: unknown
  ): void {
    const event = new CustomEvent('performance-change', {
      detail: {
        type,
        theme,
        metrics: { ...this.metrics },
        timestamp: Date.now(),
        details,
      } as PerformanceEvent,
    });

    this.dispatchEvent(event);
  }

  /**
   * 获取配置
   */
  getConfig(): PerformanceConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(updates: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...updates };

    // 重新初始化相关功能
    if (updates.enablePreloading && !this.config.enablePreloading) {
      this.preloadThemes();
    }

    if (updates.compressionEnabled !== undefined) {
      this.setupCompressionWorker();
    }

    this.emitEvent('performance-update');
  }

  /**
   * 获取加载状态
   */
  getLoadingState(): LoadingState {
    return { ...this.loadingState };
  }

  /**
   * 获取性能指标
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear();
    this.updateCacheMetrics();
    this.emitEvent('performance-update');
  }

  /**
   * 预热缓存
   */
  async warmupCache(themes: string[]): Promise<void> {
    const promises = themes.map((theme) => this.loadTheme(theme, true));
    await Promise.allSettled(promises);
  }

  /**
   * 获取缓存信息
   */
  getCacheInfo(): {
    size: number;
    items: number;
    hitRate: number;
    memoryUsage: number;
  } {
    return {
      size: this.getCurrentCacheSize(),
      items: this.cache.size,
      hitRate: this.metrics.cacheHitRate,
      memoryUsage: this.getCurrentCacheSize(), // 使用缓存大小作为内存使用量
    };
  }

  /**
   * 优化性能
   */
  optimizePerformance(): void {
    // 清理过期缓存
    this.cleanupCache();

    // 预加载常用主题
    if (this.config.enablePreloading) {
      this.preloadThemes();
    }

    // 压缩缓存数据
    if (this.config.compressionEnabled) {
      this.compressCachedData();
    }

    this.emitEvent('performance-update');
  }

  /**
   * 压缩已缓存的数据
   */
  private async compressCachedData(): Promise<void> {
    for (const [key, item] of this.cache.entries()) {
      if (!item.compressed && this.compressionWorker) {
        try {
          const compressed = await this.compressData(item.data);
          item.data = compressed;
          item.compressed = true;
          item.size = this.estimateSize(compressed);
        } catch (error) {
          if (process.env.NODE_ENV === 'development') {
            if (process.env.NODE_ENV === 'development') {
              if (process.env.NODE_ENV === 'development') {
                console.warn(`Failed to compress cached theme ${key}:`, error);
              }
            }
          }
        }
      }
    }

    this.updateCacheMetrics();
  }

  /**
   * 重置性能指标
   */
  resetMetrics(): void {
    this.metrics = {
      loadTime: 0,
      cacheHitRate: 0,
      memoryUsage: 0,
      networkRequests: 0,
      compressionRatio: 0,
      errorRate: 0,
      averageLoadTime: 0,
      peakMemoryUsage: 0,
      totalThemesLoaded: 0,
      cacheSize: 0,
    };

    this.loadTimes = [];
    this.emitEvent('performance-update');
  }

  /**
   * 获取性能报告
   */
  getPerformanceReport(): {
    summary: PerformanceMetrics;
    cacheInfo: {
      size: number;
      hitRate: number;
      items: number;
      memoryUsage: number;
    };
    loadingState: LoadingState;
    recommendations: string[];
  } {
    const recommendations: string[] = [];

    // 生成性能建议
    if (this.metrics.cacheHitRate < 50) {
      recommendations.push('考虑增加缓存大小或预加载更多主题');
    }

    if (this.metrics.averageLoadTime > 1000) {
      recommendations.push('考虑启用压缩或优化网络请求');
    }

    if (this.metrics.memoryUsage > this.config.memoryThreshold * 0.8) {
      recommendations.push('考虑清理缓存或减少缓存大小');
    }

    if (!this.config.enablePreloading) {
      recommendations.push('考虑启用主题预加载以提升用户体验');
    }

    return {
      summary: this.getMetrics(),
      cacheInfo: this.getCacheInfo(),
      loadingState: this.getLoadingState(),
      recommendations,
    };
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    // 清理 Worker
    if (this.compressionWorker) {
      this.compressionWorker.terminate();
      this.compressionWorker = null;
    }

    // 清理缓存
    this.cache.clear();

    // 清理预加载 Promise
    this.preloadPromises.clear();

    // 重置状态
    this.isInitialized = false;
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized;
  }
}

// 创建全局实例
export const themePerformanceManager = new ThemePerformanceManager();
