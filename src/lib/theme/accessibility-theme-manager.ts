/**
 * 无障碍主题管理器
 *
 * 功能特性：
 * - 高对比度主题支持
 * - 大字体缩放功能
 * - 减少动画模式
 * - 色盲友好模式
 * - 系统偏好检测
 * - 辅助技术支持
 * - 无障碍设置管理
 */

// 无障碍配置接口
export interface AccessibilityConfig {
  highContrast: boolean;
  reducedMotion: boolean;
  largeText: boolean;
  colorBlindFriendly: boolean;
  fontSize: number; // 字体缩放比例 (1.0 = 100%)
  contrastLevel: 'normal' | 'high' | 'higher' | 'maximum';
  colorBlindType:
    | 'none'
    | 'protanopia'
    | 'deuteranopia'
    | 'tritanopia'
    | 'achromatopsia';
  screenReaderOptimized: boolean;
  focusIndicatorEnhanced: boolean;
  animationDuration: number; // 动画持续时间倍数 (0 = 禁用)
}

// 系统偏好检测结果
export interface SystemPreferences {
  prefersReducedMotion: boolean;
  prefersHighContrast: boolean;
  prefersColorScheme: 'light' | 'dark' | 'no-preference';
  fontSize: number;
  forcedColors: boolean;
}

// 无障碍事件类型
export type AccessibilityEventType =
  | 'config-changed'
  | 'high-contrast-toggled'
  | 'motion-reduced'
  | 'font-size-changed'
  | 'color-blind-mode-changed'
  | 'screen-reader-detected';

export interface AccessibilityEvent {
  type: AccessibilityEventType;
  config: AccessibilityConfig;
  timestamp: number;
  source: 'user' | 'system' | 'auto';
}

/**
 * 无障碍主题管理器核心类
 */
export class AccessibilityThemeManager extends EventTarget {
  private config: AccessibilityConfig;
  private readonly systemPreferences: SystemPreferences;
  private readonly mediaQueries: Map<string, MediaQueryList> = new Map();
  private readonly mediaQueryHandlers: Map<string, (e: MediaQueryListEvent) => void> =
    new Map();
  private isInitialized = false;

  constructor(initialConfig?: Partial<AccessibilityConfig>) {
    super();

    // 默认配置
    this.config = {
      highContrast: false,
      reducedMotion: false,
      largeText: false,
      colorBlindFriendly: false,
      fontSize: 1.0,
      contrastLevel: 'normal',
      colorBlindType: 'none',
      screenReaderOptimized: false,
      focusIndicatorEnhanced: false,
      animationDuration: 1.0,
      ...initialConfig,
    };

    // 初始化系统偏好
    this.systemPreferences = {
      prefersReducedMotion: false,
      prefersHighContrast: false,
      prefersColorScheme: 'no-preference',
      fontSize: 1.0,
      forcedColors: false,
    };

    this.initialize();
  }

  /**
   * 初始化管理器
   */
  private initialize(): void {
    if (typeof window === 'undefined') {
      return;
    }

    this.detectSystemPreferences();
    this.setupMediaQueryListeners();
    this.loadStoredConfig();
    this.applyAccessibilitySettings();
    this.detectScreenReader();

    this.isInitialized = true;
    this.emitEvent('config-changed', 'system');
  }

  /**
   * 检测系统无障碍偏好
   */
  private detectSystemPreferences(): void {
    if (typeof window === 'undefined') {
      return;
    }

    // 检测减少动画偏好
    const reducedMotionQuery = window.matchMedia(
      '(prefers-reduced-motion: reduce)'
    );
    this.systemPreferences.prefersReducedMotion = reducedMotionQuery.matches;

    // 检测高对比度偏好
    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
    this.systemPreferences.prefersHighContrast = highContrastQuery.matches;

    // 检测颜色方案偏好
    const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const lightModeQuery = window.matchMedia('(prefers-color-scheme: light)');

    if (darkModeQuery.matches) {
      this.systemPreferences.prefersColorScheme = 'dark';
    } else if (lightModeQuery.matches) {
      this.systemPreferences.prefersColorScheme = 'light';
    }

    // 检测强制颜色模式
    const forcedColorsQuery = window.matchMedia('(forced-colors: active)');
    this.systemPreferences.forcedColors = forcedColorsQuery.matches;

    // 检测系统字体大小
    this.detectSystemFontSize();
  }

  /**
   * 检测系统字体大小
   */
  private detectSystemFontSize(): void {
    if (typeof window === 'undefined') {
      return;
    }

    try {
      const testElement = document.createElement('div');
      testElement.style.cssText =
        'font-size: 1rem; position: absolute; visibility: hidden;';
      document.body.appendChild(testElement);

      const computedSize = window.getComputedStyle(testElement).fontSize;
      const sizeInPx = Number.parseFloat(computedSize);

      // 标准浏览器默认字体大小为 16px
      this.systemPreferences.fontSize = sizeInPx / 16;

      document.body.removeChild(testElement);
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Failed to detect system font size:', error);
        }
      }
      this.systemPreferences.fontSize = 1.0;
    }
  }

  /**
   * 设置媒体查询监听器
   */
  private setupMediaQueryListeners(): void {
    if (typeof window === 'undefined') {
      return;
    }

    const queries = [
      { name: 'reduced-motion', query: '(prefers-reduced-motion: reduce)' },
      { name: 'high-contrast', query: '(prefers-contrast: high)' },
      { name: 'dark-mode', query: '(prefers-color-scheme: dark)' },
      { name: 'light-mode', query: '(prefers-color-scheme: light)' },
      { name: 'forced-colors', query: '(forced-colors: active)' },
    ];

    queries.forEach(({ name, query }) => {
      const mediaQuery = window.matchMedia(query);
      this.mediaQueries.set(name, mediaQuery);

      const handler = (e: MediaQueryListEvent) => {
        this.handleSystemPreferenceChange(name, e.matches);
      };

      this.mediaQueryHandlers.set(name, handler);
      mediaQuery.addEventListener('change', handler);
    });
  }

  /**
   * 处理系统偏好变化
   */
  private handleSystemPreferenceChange(
    preference: string,
    matches: boolean
  ): void {
    let configChanged = false;

    switch (preference) {
      case 'reduced-motion':
        this.systemPreferences.prefersReducedMotion = matches;
        if (matches && !this.config.reducedMotion) {
          this.config.reducedMotion = true;
          this.config.animationDuration = 0;
          configChanged = true;
        }
        break;

      case 'high-contrast':
        this.systemPreferences.prefersHighContrast = matches;
        if (matches && !this.config.highContrast) {
          this.config.highContrast = true;
          this.config.contrastLevel = 'high';
          configChanged = true;
        }
        break;

      case 'forced-colors':
        this.systemPreferences.forcedColors = matches;
        if (matches) {
          this.config.highContrast = true;
          this.config.contrastLevel = 'maximum';
          configChanged = true;
        }
        break;
    }

    if (configChanged) {
      this.applyAccessibilitySettings();
      this.saveConfig();
      this.emitEvent('config-changed', 'system');
    }
  }

  /**
   * 检测屏幕阅读器
   */
  private detectScreenReader(): void {
    if (typeof window === 'undefined') {
      return;
    }

    // 检测常见的屏幕阅读器
    const userAgent = navigator.userAgent.toLowerCase();
    const screenReaderIndicators = [
      'nvda',
      'jaws',
      'voiceover',
      'talkback',
      'orca',
    ];

    const hasScreenReader = screenReaderIndicators.some((indicator) =>
      userAgent.includes(indicator)
    );

    // 检测辅助技术 API
    const hasAccessibilityAPI =
      'speechSynthesis' in window ||
      'webkitSpeechRecognition' in window ||
      navigator.userAgent.includes('Accessibility');

    if (hasScreenReader || hasAccessibilityAPI) {
      this.config.screenReaderOptimized = true;
      this.config.focusIndicatorEnhanced = true;
      this.emitEvent('screen-reader-detected', 'system');
    }
  }

  /**
   * 应用无障碍设置
   */
  private applyAccessibilitySettings(): void {
    if (typeof document === 'undefined') {
      return;
    }

    const root = document.documentElement;

    // 应用高对比度
    if (this.config.highContrast) {
      root.classList.add('accessibility-high-contrast');
      root.setAttribute('data-contrast-level', this.config.contrastLevel);
    } else {
      root.classList.remove('accessibility-high-contrast');
      root.removeAttribute('data-contrast-level');
    }

    // 应用减少动画
    if (this.config.reducedMotion) {
      root.classList.add('accessibility-reduced-motion');
      root.style.setProperty(
        '--animation-duration-multiplier',
        this.config.animationDuration.toString()
      );
    } else {
      root.classList.remove('accessibility-reduced-motion');
      root.style.removeProperty('--animation-duration-multiplier');
    }

    // 应用大字体
    if (this.config.largeText) {
      root.classList.add('accessibility-large-text');
    } else {
      root.classList.remove('accessibility-large-text');
    }

    // 应用字体缩放
    root.style.setProperty(
      '--accessibility-font-scale',
      this.config.fontSize.toString()
    );

    // 应用色盲友好模式
    if (this.config.colorBlindFriendly) {
      root.classList.add('accessibility-color-blind-friendly');
      root.setAttribute('data-color-blind-type', this.config.colorBlindType);
    } else {
      root.classList.remove('accessibility-color-blind-friendly');
      root.removeAttribute('data-color-blind-type');
    }

    // 应用屏幕阅读器优化
    if (this.config.screenReaderOptimized) {
      root.classList.add('accessibility-screen-reader-optimized');
    } else {
      root.classList.remove('accessibility-screen-reader-optimized');
    }

    // 应用增强焦点指示器
    if (this.config.focusIndicatorEnhanced) {
      root.classList.add('accessibility-enhanced-focus');
    } else {
      root.classList.remove('accessibility-enhanced-focus');
    }
  }

  /**
   * 从本地存储加载配置
   */
  private loadStoredConfig(): void {
    if (typeof localStorage === 'undefined') {
      return;
    }

    try {
      const stored = localStorage.getItem('accessibility-config');
      if (stored) {
        const parsedConfig = JSON.parse(stored);
        this.config = { ...this.config, ...parsedConfig };
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Failed to load accessibility config:', error);
        }
      }
    }
  }

  /**
   * 保存配置到本地存储
   */
  private saveConfig(): void {
    if (typeof localStorage === 'undefined') {
      return;
    }

    try {
      localStorage.setItem('accessibility-config', JSON.stringify(this.config));
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Failed to save accessibility config:', error);
        }
      }
    }
  }

  /**
   * 发出事件
   */
  private emitEvent(
    type: AccessibilityEventType,
    source: 'user' | 'system' | 'auto'
  ): void {
    const event = new CustomEvent('accessibility-change', {
      detail: {
        type,
        config: { ...this.config },
        timestamp: Date.now(),
        source,
      } as AccessibilityEvent,
    });

    this.dispatchEvent(event);
  }

  /**
   * 获取当前配置
   */
  getConfig(): AccessibilityConfig {
    return { ...this.config };
  }

  /**
   * 获取系统偏好
   */
  getSystemPreferences(): SystemPreferences {
    return { ...this.systemPreferences };
  }

  /**
   * 更新配置
   */
  updateConfig(updates: Partial<AccessibilityConfig>): void {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...updates };

    this.applyAccessibilitySettings();
    this.saveConfig();

    // 检查特定变化并发出相应事件
    if (oldConfig.highContrast !== this.config.highContrast) {
      this.emitEvent('high-contrast-toggled', 'user');
    }
    if (oldConfig.reducedMotion !== this.config.reducedMotion) {
      this.emitEvent('motion-reduced', 'user');
    }
    if (oldConfig.fontSize !== this.config.fontSize) {
      this.emitEvent('font-size-changed', 'user');
    }
    if (oldConfig.colorBlindType !== this.config.colorBlindType) {
      this.emitEvent('color-blind-mode-changed', 'user');
    }

    this.emitEvent('config-changed', 'user');
  }

  /**
   * 切换高对比度模式
   */
  toggleHighContrast(): void {
    this.updateConfig({ highContrast: !this.config.highContrast });
  }

  /**
   * 切换减少动画模式
   */
  toggleReducedMotion(): void {
    const reducedMotion = !this.config.reducedMotion;
    this.updateConfig({
      reducedMotion,
      animationDuration: reducedMotion ? 0 : 1.0,
    });
  }

  /**
   * 切换大字体模式
   */
  toggleLargeText(): void {
    this.updateConfig({ largeText: !this.config.largeText });
  }

  /**
   * 设置字体缩放
   */
  setFontScale(scale: number): void {
    const clampedScale = Math.max(0.5, Math.min(3.0, scale));
    this.updateConfig({ fontSize: clampedScale });
  }

  /**
   * 设置对比度级别
   */
  setContrastLevel(level: AccessibilityConfig['contrastLevel']): void {
    this.updateConfig({
      contrastLevel: level,
      highContrast: level !== 'normal',
    });
  }

  /**
   * 设置色盲友好模式
   */
  setColorBlindMode(type: AccessibilityConfig['colorBlindType']): void {
    this.updateConfig({
      colorBlindType: type,
      colorBlindFriendly: type !== 'none',
    });
  }

  /**
   * 重置为默认设置
   */
  resetToDefaults(): void {
    const defaultConfig: AccessibilityConfig = {
      highContrast: false,
      reducedMotion: false,
      largeText: false,
      colorBlindFriendly: false,
      fontSize: 1.0,
      contrastLevel: 'normal',
      colorBlindType: 'none',
      screenReaderOptimized: false,
      focusIndicatorEnhanced: false,
      animationDuration: 1.0,
    };

    this.config = defaultConfig;
    this.applyAccessibilitySettings();
    this.saveConfig();
    this.emitEvent('config-changed', 'user');
  }

  /**
   * 应用系统推荐设置
   */
  applySystemRecommendations(): void {
    const updates: Partial<AccessibilityConfig> = {};

    if (this.systemPreferences.prefersReducedMotion) {
      updates.reducedMotion = true;
      updates.animationDuration = 0;
    }

    if (
      this.systemPreferences.prefersHighContrast ||
      this.systemPreferences.forcedColors
    ) {
      updates.highContrast = true;
      updates.contrastLevel = this.systemPreferences.forcedColors
        ? 'maximum'
        : 'high';
    }

    if (this.systemPreferences.fontSize > 1.2) {
      updates.largeText = true;
      updates.fontSize = this.systemPreferences.fontSize;
    }

    this.updateConfig(updates);
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    // 移除媒体查询监听器
    this.mediaQueries.forEach((mediaQuery, name) => {
      const handler = this.mediaQueryHandlers.get(name);
      if (handler) {
        mediaQuery.removeEventListener('change', handler);
      }
    });
    this.mediaQueries.clear();
    this.mediaQueryHandlers.clear();

    // 移除所有事件监听器
    this.removeEventListener('accessibility-change', () => {});
  }
}

// 创建全局实例
export const accessibilityThemeManager = new AccessibilityThemeManager();
