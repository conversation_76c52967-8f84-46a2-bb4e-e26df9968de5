/**
 * 智能主题适配系统
 *
 * 功能特性：
 * - 基于时间的自动主题切换
 * - 环境光线检测和适配
 * - 用户行为学习和偏好分析
 * - 智能推荐算法
 * - 地理位置和时区感知
 * - 企业级配置和管理
 */
// next-themes 不导出 Theme 类型，我们使用字符串类型

// 支持的主题类型
export type SupportedTheme = 'light' | 'dark' | 'auto' | 'system';

// 时间段类型
export type TimeOfDay =
  | 'dawn'
  | 'morning'
  | 'afternoon'
  | 'evening'
  | 'night'
  | 'late-night';

// 环境光线级别
export type AmbientLightLevel =
  | 'very-bright'
  | 'bright'
  | 'normal'
  | 'dim'
  | 'dark'
  | 'very-dark';

// 用户活动类型
export type UserActivity =
  | 'reading'
  | 'coding'
  | 'browsing'
  | 'media'
  | 'gaming'
  | 'idle';

// 主题适配原因
export type AdaptationReason =
  | 'time-based' // 基于时间
  | 'ambient-light' // 环境光线
  | 'user-preference' // 用户偏好
  | 'activity-based' // 活动类型
  | 'location-based' // 地理位置
  | 'energy-saving' // 节能模式
  | 'accessibility'; // 无障碍需求

// 主题适配建议
export interface ThemeAdaptationSuggestion {
  suggestedTheme: SupportedTheme;
  confidence: number; // 置信度 (0-1)
  reason: AdaptationReason;
  reasoning: string; // 推理说明
  priority: number; // 优先级 (1-10)
  metadata: {
    timeOfDay?: TimeOfDay;
    ambientLight?: AmbientLightLevel;
    userActivity?: UserActivity;
    location?: {
      timezone: string;
      latitude?: number;
      longitude?: number;
    };
    energyLevel?: number; // 设备电量 (0-100)
    accessibility?: {
      highContrast: boolean;
      reducedMotion: boolean;
      largeText: boolean;
    };
  };
  timestamp: number;
}

// 用户行为数据
export interface UserBehaviorData {
  themeUsage: Record<
    SupportedTheme,
    {
      totalTime: number; // 总使用时间（毫秒）
      sessionCount: number; // 使用次数
      timeDistribution: Record<TimeOfDay, number>; // 时间分布
      activityDistribution: Record<UserActivity, number>; // 活动分布
    }
  >;
  preferences: {
    preferredTheme: SupportedTheme;
    autoSwitchEnabled: boolean;
    adaptationSensitivity: number; // 适配敏感度 (0-1)
    lastManualChange: number; // 最后手动更改时间
  };
  patterns: {
    morningTheme: SupportedTheme;
    afternoonTheme: SupportedTheme;
    eveningTheme: SupportedTheme;
    nightTheme: SupportedTheme;
  };
  statistics: {
    totalSessions: number;
    averageSessionDuration: number;
    mostUsedTheme: SupportedTheme;
    adaptationAcceptanceRate: number; // 适配建议接受率
  };
}

// 环境感知数据
export interface EnvironmentData {
  time: {
    current: Date;
    timeOfDay: TimeOfDay;
    timezone: string;
    sunrise?: Date;
    sunset?: Date;
  };
  light: {
    level: AmbientLightLevel;
    lux?: number; // 光照强度
    source: 'sensor' | 'estimated' | 'manual';
  };
  location?: {
    timezone: string;
    latitude?: number;
    longitude?: number;
    city?: string;
    country?: string;
  };
  device: {
    batteryLevel?: number; // 电池电量 (0-100)
    powerSaving?: boolean; // 省电模式
    screenBrightness?: number; // 屏幕亮度 (0-100)
  };
  accessibility: {
    highContrast: boolean;
    reducedMotion: boolean;
    largeText: boolean;
    colorBlindness?: 'none' | 'protanopia' | 'deuteranopia' | 'tritanopia';
  };
}

/**
 * 智能主题适配器类
 */
export class SmartThemeAdapter {
  private userBehavior: UserBehaviorData;
  private adaptationHistory: ThemeAdaptationSuggestion[] = [];
  private readonly isLearningEnabled: boolean;
  private readonly adaptationSensitivity: number;

  constructor(
    options: {
      isLearningEnabled?: boolean;
      adaptationSensitivity?: number;
      initialUserBehavior?: Partial<UserBehaviorData>;
    } = {}
  ) {
    this.isLearningEnabled = options.isLearningEnabled ?? true;
    this.adaptationSensitivity = options.adaptationSensitivity ?? 0.7;

    // 初始化用户行为数据
    this.userBehavior = this.initializeUserBehavior(
      options.initialUserBehavior
    );

    // 从本地存储加载数据
    this.loadUserBehaviorData();
  }

  /**
   * 初始化用户行为数据
   */
  private initializeUserBehavior(
    initial?: Partial<UserBehaviorData>
  ): UserBehaviorData {
    const defaultBehavior: UserBehaviorData = {
      themeUsage: {
        light: {
          totalTime: 0,
          sessionCount: 0,
          timeDistribution: {} as Record<TimeOfDay, number>,
          activityDistribution: {} as Record<UserActivity, number>,
        },
        dark: {
          totalTime: 0,
          sessionCount: 0,
          timeDistribution: {} as Record<TimeOfDay, number>,
          activityDistribution: {} as Record<UserActivity, number>,
        },
        auto: {
          totalTime: 0,
          sessionCount: 0,
          timeDistribution: {} as Record<TimeOfDay, number>,
          activityDistribution: {} as Record<UserActivity, number>,
        },
        system: {
          totalTime: 0,
          sessionCount: 0,
          timeDistribution: {} as Record<TimeOfDay, number>,
          activityDistribution: {} as Record<UserActivity, number>,
        },
      },
      preferences: {
        preferredTheme: 'auto',
        autoSwitchEnabled: true,
        adaptationSensitivity: this.adaptationSensitivity,
        lastManualChange: 0,
      },
      patterns: {
        morningTheme: 'light',
        afternoonTheme: 'light',
        eveningTheme: 'dark',
        nightTheme: 'dark',
      },
      statistics: {
        totalSessions: 0,
        averageSessionDuration: 0,
        mostUsedTheme: 'auto',
        adaptationAcceptanceRate: 0.8,
      },
    };

    return { ...defaultBehavior, ...initial };
  }

  /**
   * 获取当前时间段
   */
  private getTimeOfDay(date: Date = new Date()): TimeOfDay {
    const hour = date.getHours();

    if (hour >= 5 && hour < 7) return 'dawn';
    if (hour >= 7 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 17) return 'afternoon';
    if (hour >= 17 && hour < 20) return 'evening';
    if (hour >= 20 && hour < 23) return 'night';
    return 'late-night';
  }

  /**
   * 估算环境光线级别
   */
  private estimateAmbientLight(
    timeOfDay: TimeOfDay,
    screenBrightness?: number
  ): AmbientLightLevel {
    // 基于时间段的基础估算
    const timeBasedLight: Record<TimeOfDay, AmbientLightLevel> = {
      dawn: 'dim',
      morning: 'bright',
      afternoon: 'very-bright',
      evening: 'normal',
      night: 'dim',
      'late-night': 'dark',
    };

    let baseLevel = timeBasedLight[timeOfDay];

    // 如果有屏幕亮度信息，进行调整
    if (screenBrightness !== undefined) {
      if (screenBrightness < 20) baseLevel = 'dark';
      else if (screenBrightness < 40) baseLevel = 'dim';
      else if (screenBrightness < 60) baseLevel = 'normal';
      else if (screenBrightness < 80) baseLevel = 'bright';
      else baseLevel = 'very-bright';
    }

    return baseLevel;
  }

  /**
   * 检测用户活动类型
   */
  private detectUserActivity(): UserActivity {
    // 简化的活动检测逻辑
    // 实际实现中可以基于页面类型、用户交互等进行检测
    const currentHour = new Date().getHours();

    if (currentHour >= 9 && currentHour < 17) {
      return 'coding'; // 工作时间假设为编程
    } else if (currentHour >= 17 && currentHour < 22) {
      return 'browsing'; // 晚上假设为浏览
    } else {
      return 'reading'; // 其他时间假设为阅读
    }
  }

  /**
   * 生成主题适配建议
   */
  async generateAdaptationSuggestion(
    currentTheme: SupportedTheme,
    environmentData?: Partial<EnvironmentData>
  ): Promise<ThemeAdaptationSuggestion> {
    const now = new Date();
    const timeOfDay = this.getTimeOfDay(now);
    const userActivity = this.detectUserActivity();

    // 构建完整的环境数据
    const fullEnvironmentData: EnvironmentData = {
      time: {
        current: now,
        timeOfDay,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        ...environmentData?.time,
      },
      light: {
        level: this.estimateAmbientLight(
          timeOfDay,
          environmentData?.device?.screenBrightness
        ),
        source: 'estimated',
        ...environmentData?.light,
      },
      location: environmentData?.location || {
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      },
      device: {
        batteryLevel: 100,
        powerSaving: false,
        screenBrightness: 50,
        ...environmentData?.device,
      },
      accessibility: {
        highContrast: false,
        reducedMotion: false,
        largeText: false,
        ...environmentData?.accessibility,
      },
    };

    // 生成多个适配建议
    const suggestions = await Promise.all([
      this.generateTimeBasedSuggestion(fullEnvironmentData),
      this.generateLightBasedSuggestion(fullEnvironmentData),
      this.generateUserPreferenceSuggestion(fullEnvironmentData),
      this.generateActivityBasedSuggestion(fullEnvironmentData, userActivity),
      this.generateAccessibilitySuggestion(fullEnvironmentData),
    ]);

    // 选择最佳建议
    const bestSuggestion = suggestions.reduce((best, current) =>
      current.confidence * current.priority > best.confidence * best.priority
        ? current
        : best
    );

    // 记录适配历史
    this.adaptationHistory.push(bestSuggestion);

    // 限制历史记录长度
    if (this.adaptationHistory.length > 100) {
      this.adaptationHistory = this.adaptationHistory.slice(-50);
    }

    return bestSuggestion;
  }

  /**
   * 基于时间的主题建议
   */
  private async generateTimeBasedSuggestion(
    env: EnvironmentData
  ): Promise<ThemeAdaptationSuggestion> {
    const { timeOfDay } = env.time;
    const userPattern = this.userBehavior.patterns;

    let suggestedTheme: SupportedTheme;
    let confidence = 0.8;

    switch (timeOfDay) {
      case 'dawn':
      case 'morning':
        suggestedTheme = userPattern.morningTheme;
        break;
      case 'afternoon':
        suggestedTheme = userPattern.afternoonTheme;
        break;
      case 'evening':
        suggestedTheme = userPattern.eveningTheme;
        break;
      case 'night':
      case 'late-night':
        suggestedTheme = userPattern.nightTheme;
        confidence = 0.9; // 夜间更倾向于深色主题
        break;
      default:
        suggestedTheme = 'auto';
    }

    return {
      suggestedTheme,
      confidence,
      reason: 'time-based',
      reasoning: `基于当前时间段（${timeOfDay}）和用户历史偏好推荐`,
      priority: 7,
      metadata: {
        timeOfDay,
        ...(env.location && { location: env.location }),
      },
      timestamp: Date.now(),
    };
  }

  /**
   * 基于环境光线的主题建议
   */
  private async generateLightBasedSuggestion(
    env: EnvironmentData
  ): Promise<ThemeAdaptationSuggestion> {
    const { level: lightLevel } = env.light;

    let suggestedTheme: SupportedTheme;
    let confidence = 0.7;

    switch (lightLevel) {
      case 'very-bright':
      case 'bright':
        suggestedTheme = 'light';
        confidence = 0.8;
        break;
      case 'normal':
        suggestedTheme = 'auto';
        confidence = 0.6;
        break;
      case 'dim':
      case 'dark':
      case 'very-dark':
        suggestedTheme = 'dark';
        confidence = 0.9;
        break;
      default:
        suggestedTheme = 'auto';
    }

    return {
      suggestedTheme,
      confidence,
      reason: 'ambient-light',
      reasoning: `基于环境光线级别（${lightLevel}）推荐`,
      priority: 8,
      metadata: {
        ambientLight: lightLevel,
      },
      timestamp: Date.now(),
    };
  }

  /**
   * 基于用户偏好的主题建议
   */
  private async generateUserPreferenceSuggestion(
    env: EnvironmentData
  ): Promise<ThemeAdaptationSuggestion> {
    const { preferredTheme } = this.userBehavior.preferences;
    const { mostUsedTheme } = this.userBehavior.statistics;

    return {
      suggestedTheme:
        preferredTheme !== 'auto' ? preferredTheme : mostUsedTheme,
      confidence: 0.9,
      reason: 'user-preference',
      reasoning: `基于用户偏好设置和使用习惯推荐`,
      priority: 9,
      metadata: {},
      timestamp: Date.now(),
    };
  }

  /**
   * 基于活动类型的主题建议
   */
  private async generateActivityBasedSuggestion(
    env: EnvironmentData,
    activity: UserActivity
  ): Promise<ThemeAdaptationSuggestion> {
    let suggestedTheme: SupportedTheme;
    let confidence = 0.6;

    switch (activity) {
      case 'coding':
        suggestedTheme = 'dark'; // 编程通常偏好深色主题
        confidence = 0.8;
        break;
      case 'reading':
        suggestedTheme = env.light.level === 'bright' ? 'light' : 'dark';
        confidence = 0.7;
        break;
      case 'media':
        suggestedTheme = 'dark'; // 媒体消费偏好深色
        confidence = 0.7;
        break;
      default:
        suggestedTheme = 'auto';
    }

    return {
      suggestedTheme,
      confidence,
      reason: 'activity-based',
      reasoning: `基于当前活动类型（${activity}）推荐`,
      priority: 6,
      metadata: {
        userActivity: activity,
      },
      timestamp: Date.now(),
    };
  }

  /**
   * 基于无障碍需求的主题建议
   */
  private async generateAccessibilitySuggestion(
    env: EnvironmentData
  ): Promise<ThemeAdaptationSuggestion> {
    const { accessibility } = env;

    if (accessibility.highContrast) {
      return {
        suggestedTheme: 'dark',
        confidence: 0.95,
        reason: 'accessibility',
        reasoning: '基于高对比度需求推荐深色主题',
        priority: 10, // 无障碍需求优先级最高
        metadata: {
          accessibility,
        },
        timestamp: Date.now(),
      };
    }

    return {
      suggestedTheme: 'auto',
      confidence: 0.5,
      reason: 'accessibility',
      reasoning: '无特殊无障碍需求，使用自动主题',
      priority: 3,
      metadata: {
        accessibility,
      },
      timestamp: Date.now(),
    };
  }

  /**
   * 记录用户主题使用行为
   */
  recordThemeUsage(
    theme: SupportedTheme,
    duration: number,
    activity: UserActivity = 'browsing'
  ): void {
    if (!this.isLearningEnabled) return;

    const timeOfDay = this.getTimeOfDay();
    const usage = this.userBehavior.themeUsage[theme];

    usage.totalTime += duration;
    usage.sessionCount += 1;
    usage.timeDistribution[timeOfDay] =
      (usage.timeDistribution[timeOfDay] || 0) + duration;
    usage.activityDistribution[activity] =
      (usage.activityDistribution[activity] || 0) + duration;

    // 更新统计信息
    this.updateStatistics();

    // 保存到本地存储
    this.saveUserBehaviorData();
  }

  /**
   * 记录适配建议的接受情况
   */
  recordAdaptationFeedback(suggestionId: string, accepted: boolean): void {
    // 更新接受率统计
    const currentRate = this.userBehavior.statistics.adaptationAcceptanceRate;
    const totalSuggestions = this.adaptationHistory.length;

    if (totalSuggestions > 0) {
      const newRate = accepted
        ? (currentRate * (totalSuggestions - 1) + 1) / totalSuggestions
        : (currentRate * (totalSuggestions - 1)) / totalSuggestions;

      this.userBehavior.statistics.adaptationAcceptanceRate = newRate;
    }

    this.saveUserBehaviorData();
  }

  /**
   * 更新统计信息
   */
  private updateStatistics(): void {
    const stats = this.userBehavior.statistics;
    const usage = this.userBehavior.themeUsage;

    // 计算总会话数
    stats.totalSessions = Object.values(usage).reduce(
      (sum, theme) => sum + theme.sessionCount,
      0
    );

    // 计算平均会话时长
    const totalTime = Object.values(usage).reduce(
      (sum, theme) => sum + theme.totalTime,
      0
    );
    stats.averageSessionDuration =
      stats.totalSessions > 0 ? totalTime / stats.totalSessions : 0;

    // 找出最常用的主题
    let maxUsage = 0;
    let mostUsed: SupportedTheme = 'auto';

    for (const [theme, data] of Object.entries(usage)) {
      if (data.totalTime > maxUsage) {
        maxUsage = data.totalTime;
        mostUsed = theme as SupportedTheme;
      }
    }

    stats.mostUsedTheme = mostUsed;
  }

  /**
   * 从本地存储加载用户行为数据
   */
  private loadUserBehaviorData(): void {
    if (typeof window === 'undefined') return;

    try {
      const stored = localStorage.getItem('smart-theme-adapter-behavior');
      if (stored) {
        const data = JSON.parse(stored);
        this.userBehavior = { ...this.userBehavior, ...data };
      }
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Failed to load user behavior data:', error);
        }
      }
    }
  }

  /**
   * 保存用户行为数据到本地存储
   */
  private saveUserBehaviorData(): void {
    if (typeof window === 'undefined') return;

    try {
      localStorage.setItem(
        'smart-theme-adapter-behavior',
        JSON.stringify(this.userBehavior)
      );
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        if (process.env.NODE_ENV === 'development') {
          console.warn('Failed to save user behavior data:', error);
        }
      }
    }
  }

  /**
   * 获取用户行为数据
   */
  getUserBehaviorData(): UserBehaviorData {
    return { ...this.userBehavior };
  }

  /**
   * 获取适配历史
   */
  getAdaptationHistory(): ThemeAdaptationSuggestion[] {
    return [...this.adaptationHistory];
  }

  /**
   * 清除所有数据
   */
  clearAllData(): void {
    this.userBehavior = this.initializeUserBehavior();
    this.adaptationHistory = [];

    if (typeof window !== 'undefined') {
      localStorage.removeItem('smart-theme-adapter-behavior');
    }
  }
}

// 全局实例
export const smartThemeAdapter = new SmartThemeAdapter({
  isLearningEnabled: true,
  adaptationSensitivity: 0.7,
});

// 所有类型已在文件顶部导出
