/**
 * 主题过渡动画管理器测试
 *
 * 测试覆盖：
 * - 过渡动画配置和管理
 * - 动画状态跟踪
 * - 颜色插值和过渡
 * - 缓动函数应用
 * - 性能监控
 * - 用户偏好检测
 * - 事件处理
 * - 边界条件和错误处理
 */
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

import {
  type AnimationState,
  type PerformanceMetrics,
  ThemeTransitionManager,
  type TransitionConfig,
  type TransitionEvent,
} from '../theme-transition-manager';

// Mock requestAnimationFrame
let animationFrameId = 0;
const mockRequestAnimationFrame = vi.fn((callback: FrameRequestCallback) => {
  animationFrameId++;
  setTimeout(() => callback(performance.now()), 16);
  return animationFrameId;
});

const mockCancelAnimationFrame = vi.fn((id: number) => {
  // Mock implementation
});

// Mock performance API
const mockPerformance = {
  now: vi.fn(() => Date.now()),
  mark: vi.fn(),
  measure: vi.fn(),
};

// Mock DOM APIs
const mockElement = {
  style: {
    setProperty: vi.fn(),
    removeProperty: vi.fn(),
  },
  classList: {
    add: vi.fn(),
    remove: vi.fn(),
  },
  getAttribute: vi.fn(),
  setAttribute: vi.fn(),
};

const mockDocument = {
  documentElement: mockElement,
};

const mockWindow = {
  requestAnimationFrame: mockRequestAnimationFrame,
  cancelAnimationFrame: mockCancelAnimationFrame,
  performance: mockPerformance,
  matchMedia: vi.fn(() => ({
    matches: false,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
  })),
  getComputedStyle: vi.fn(() => ({
    getPropertyValue: vi.fn(() => 'hsl(0, 0%, 100%)'),
  })),
};

// Mock globals
Object.defineProperty(global, 'window', { value: mockWindow });
Object.defineProperty(global, 'document', { value: mockDocument });
Object.defineProperty(global, 'requestAnimationFrame', {
  value: mockRequestAnimationFrame,
});
Object.defineProperty(global, 'cancelAnimationFrame', {
  value: mockCancelAnimationFrame,
});
Object.defineProperty(global, 'performance', { value: mockPerformance });

describe('主题过渡动画管理器', () => {
  let manager: ThemeTransitionManager;

  beforeEach(() => {
    vi.clearAllMocks();
    animationFrameId = 0;

    manager = new ThemeTransitionManager({
      type: 'fade',
      duration: 300,
      delay: 0,
      easing: 'ease-out',
      respectMotionPreference: true,
    });
  });

  afterEach(() => {
    manager.destroy();
  });

  describe('初始化和配置', () => {
    it('应该正确初始化默认配置', () => {
      const defaultManager = new ThemeTransitionManager();
      const config = defaultManager.getConfig();

      expect(config.type).toBe('fade');
      expect(config.duration).toBe(300);
      expect(config.delay).toBe(0);
      expect(config.easing).toBe('ease-out');
      expect(config.respectMotionPreference).toBe(true);

      defaultManager.destroy();
    });

    it('应该接受自定义配置', () => {
      const customManager = new ThemeTransitionManager({
        type: 'slide',
        duration: 500,
        delay: 100,
        easing: 'ease-in',
        respectMotionPreference: false,
      });

      const config = customManager.getConfig();
      expect(config.type).toBe('slide');
      expect(config.duration).toBe(500);
      expect(config.delay).toBe(100);
      expect(config.easing).toBe('ease-in');
      expect(config.respectMotionPreference).toBe(false);

      customManager.destroy();
    });

    it('应该检测用户动画偏好', () => {
      const mockMediaQuery = {
        matches: true,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };

      mockWindow.matchMedia.mockReturnValue(mockMediaQuery);

      const motionManager = new ThemeTransitionManager({
        respectMotionPreference: true,
      });

      expect(mockWindow.matchMedia).toHaveBeenCalledWith(
        '(prefers-reduced-motion: reduce)'
      );

      motionManager.destroy();
    });
  });

  describe('过渡动画控制', () => {
    it('应该正确设置动画状态', () => {
      // 测试初始状态
      const initialState = manager.getAnimationState();
      expect(initialState.isAnimating).toBe(false);
      expect(initialState.currentAnimation).toBe(null);

      // 开始过渡（会立即完成因为是测试环境）
      manager.startTransition('light', 'dark');

      // 验证状态已更新
      const state = manager.getAnimationState();
      expect(state.fromTheme).toBe('light');
      expect(state.toTheme).toBe('dark');
    });

    it('应该正确取消动画', () => {
      manager.startTransition('light', 'dark');
      manager.cancelTransition();

      const state = manager.getAnimationState();
      expect(state.isAnimating).toBe(false);
      expect(state.currentAnimation).toBe(null);
    });

    it('应该处理配置更新', () => {
      manager.updateConfig({ type: 'slide', duration: 500 });

      const config = manager.getConfig();
      expect(config.type).toBe('slide');
      expect(config.duration).toBe(500);
    });
  });

  describe('缓动函数', () => {
    it('应该正确应用线性缓动', () => {
      const linearManager = new ThemeTransitionManager({
        easing: 'linear',
      });

      // 测试线性缓动的中点
      const result = linearManager['applyEasing'](0.5);
      expect(result).toBeCloseTo(0.5, 2);

      linearManager.destroy();
    });

    it('应该正确应用ease-out缓动', () => {
      const easeOutManager = new ThemeTransitionManager({
        easing: 'ease-out',
      });

      // ease-out 在开始时变化较快
      const early = easeOutManager['applyEasing'](0.2);
      const late = easeOutManager['applyEasing'](0.8);

      expect(early).toBeGreaterThan(0.2);
      expect(late).toBeLessThan(1.0); // 调整期望值

      easeOutManager.destroy();
    });

    it('应该支持自定义贝塞尔曲线', () => {
      const customManager = new ThemeTransitionManager({
        easing: 'cubic-bezier',
        customEasing: 'cubic-bezier(0.25, 0.1, 0.25, 1)',
      });

      const result = customManager['applyEasing'](0.5);
      expect(typeof result).toBe('number');
      expect(result).toBeGreaterThanOrEqual(0);
      expect(result).toBeLessThanOrEqual(1);

      customManager.destroy();
    });
  });

  describe('颜色插值', () => {
    it('应该正确插值HSL颜色', () => {
      const fromColor = 'hsl(0, 100%, 50%)'; // 红色
      const toColor = 'hsl(120, 100%, 50%)'; // 绿色

      const midColor = manager['interpolateColor'](fromColor, toColor, 0.5);
      expect(midColor).toContain('hsl(60'); // 应该是黄色附近
    });

    it('应该处理无效的颜色格式', () => {
      const fromColor = 'invalid-color';
      const toColor = 'another-invalid-color';

      const result = manager['interpolateColor'](fromColor, toColor, 0.5);
      expect(result).toBe('another-invalid-color'); // 应该回退到目标颜色
    });

    it('应该正确解析HSL颜色', () => {
      const hslColor = 'hsl(240, 100%, 50%)';
      const parsed = manager['parseHslColor'](hslColor);

      expect(parsed).toEqual({
        h: 240,
        s: 100,
        l: 50,
      });
    });
  });

  describe('事件处理', () => {
    it('应该触发过渡开始事件', async () => {
      const eventPromise = new Promise<TransitionEvent>((resolve) => {
        manager.addEventListener('start', resolve);
      });

      manager.startTransition('light', 'dark');

      const event = await eventPromise;
      expect(event.type).toBe('start');
      expect(event.fromTheme).toBe('light');
      expect(event.toTheme).toBe('dark');
      expect(event.timestamp).toBeGreaterThan(0);
    });

    it('应该触发过渡完成事件', async () => {
      const eventPromise = new Promise<TransitionEvent>((resolve) => {
        manager.addEventListener('complete', resolve);
      });

      // 使用很短的持续时间以快速完成
      manager.updateConfig({ duration: 1 });
      manager.startTransition('light', 'dark');

      const event = await eventPromise;
      expect(event.type).toBe('complete');
      expect(event.fromTheme).toBe('light');
      expect(event.toTheme).toBe('dark');
    });

    it('应该触发过渡取消事件', () => {
      let cancelEvent: TransitionEvent | null = null;

      manager.addEventListener('cancel', (event) => {
        cancelEvent = event;
      });

      // 手动设置动画状态以模拟正在进行的动画
      manager['animationState'] = {
        isAnimating: true,
        currentAnimation: 'test-animation',
        progress: 0.5,
        startTime: Date.now(),
        endTime: Date.now() + 1000,
        fromTheme: 'light',
        toTheme: 'dark',
      };

      manager.cancelTransition();

      expect(cancelEvent).toBeTruthy();
      expect(cancelEvent?.type).toBe('cancel');
    });

    it('应该正确移除事件监听器', () => {
      const listener = vi.fn();

      manager.addEventListener('start', listener);
      manager.removeEventListener('start', listener);

      manager.startTransition('light', 'dark');

      // 监听器不应该被调用
      setTimeout(() => {
        expect(listener).not.toHaveBeenCalled();
      }, 50);
    });
  });

  describe('配置更新', () => {
    it('应该更新过渡配置', () => {
      const newConfig = {
        type: 'slide' as const,
        duration: 500,
        easing: 'ease-in' as const,
      };

      manager.updateConfig(newConfig);

      const config = manager.getConfig();
      expect(config.type).toBe('slide');
      expect(config.duration).toBe(500);
      expect(config.easing).toBe('ease-in');
    });

    it('应该保留未更新的配置项', () => {
      const originalConfig = manager.getConfig();

      manager.updateConfig({ duration: 500 });

      const newConfig = manager.getConfig();
      expect(newConfig.duration).toBe(500);
      expect(newConfig.type).toBe(originalConfig.type);
      expect(newConfig.easing).toBe(originalConfig.easing);
    });
  });

  describe('性能监控', () => {
    it('应该提供性能指标', () => {
      const metrics = manager.getPerformanceMetrics();

      if (metrics) {
        expect(typeof metrics.frameRate).toBe('number');
        expect(typeof metrics.droppedFrames).toBe('number');
        expect(typeof metrics.animationDuration).toBe('number');
        expect(typeof metrics.memoryUsage).toBe('number');
        expect(typeof metrics.cpuUsage).toBe('number');
      }
    });

    it('应该在不支持的环境中返回null', () => {
      // 临时移除 window 对象
      const originalWindow = global.window;
      delete (global as any).window;

      const noPerformanceManager = new ThemeTransitionManager();
      const metrics = noPerformanceManager.getPerformanceMetrics();

      expect(metrics).toBe(null);

      // 恢复 window 对象
      global.window = originalWindow;
      noPerformanceManager.destroy();
    });
  });

  describe('边界条件', () => {
    it('应该处理相同主题的过渡', () => {
      manager.startTransition('light', 'light');

      const state = manager.getAnimationState();
      expect(state.fromTheme).toBe('light');
      expect(state.toTheme).toBe('light');
    });

    it('应该处理空主题名称', async () => {
      await manager.startTransition('', '');

      const state = manager.getAnimationState();
      expect(state.fromTheme).toBe('');
      expect(state.toTheme).toBe('');
    });

    it('应该处理极短的动画时长', async () => {
      manager.updateConfig({ duration: 1 });

      const startTime = Date.now();
      await manager.startTransition('light', 'dark');
      const endTime = Date.now();

      // 动画应该很快完成
      expect(endTime - startTime).toBeLessThan(100);
    });

    it('应该处理极长的动画时长', () => {
      manager.updateConfig({ duration: 10000 });

      const config = manager.getConfig();
      expect(config.duration).toBe(10000);
    });
  });

  describe('内存管理', () => {
    it('应该正确清理资源', () => {
      manager.startTransition('light', 'dark');

      // 销毁管理器
      manager.destroy();

      const state = manager.getAnimationState();
      expect(state.isAnimating).toBe(false);
    });

    it('应该清理事件监听器', () => {
      const listener = vi.fn();
      manager.addEventListener('start', listener);

      manager.destroy();

      // 尝试触发事件，监听器不应该被调用
      manager['emitEvent']({
        type: 'start',
        animationId: 'test',
        fromTheme: 'light',
        toTheme: 'dark',
        timestamp: Date.now(),
      });

      expect(listener).not.toHaveBeenCalled();
    });
  });

  describe('错误处理', () => {
    it('应该处理事件监听器中的错误', () => {
      const consoleSpy = vi
        .spyOn(console, 'error')
        .mockImplementation(() => {});

      manager.addEventListener('start', () => {
        throw new Error('Test error');
      });

      // 应该不抛出错误
      expect(() => {
        manager.startTransition('light', 'dark');
      }).not.toThrow();

      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('应该处理DOM API不可用的情况', () => {
      // 临时移除 document
      const originalDocument = global.document;
      delete (global as any).document;

      const noDocManager = new ThemeTransitionManager();

      // 应该不抛出错误
      expect(() => {
        noDocManager.startTransition('light', 'dark');
      }).not.toThrow();

      // 恢复 document
      global.document = originalDocument;
      noDocManager.destroy();
    });
  });
});
