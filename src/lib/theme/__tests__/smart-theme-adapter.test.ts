/**
 * 智能主题适配系统测试
 *
 * 测试覆盖：
 * - 主题适配建议生成
 * - 时间基础适配逻辑
 * - 环境光线检测和适配
 * - 用户行为学习和记录
 * - 偏好分析和推荐
 * - 数据持久化和管理
 * - 边界条件和错误处理
 */
import { beforeEach, describe, expect, it, vi } from 'vitest';

import {
  type AmbientLightLevel,
  type EnvironmentData,
  SmartThemeAdapter,
  type SupportedTheme,
  type ThemeAdaptationSuggestion,
  type TimeOfDay,
  type UserActivity,
  type UserBehaviorData,
} from '../smart-theme-adapter';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('智能主题适配系统', () => {
  let adapter: SmartThemeAdapter;

  beforeEach(() => {
    // 清除所有 mock
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);

    // 创建新的适配器实例
    adapter = new SmartThemeAdapter({
      isLearningEnabled: true,
      adaptationSensitivity: 0.7,
    });
  });

  describe('初始化和配置', () => {
    it('应该正确初始化默认配置', () => {
      const defaultAdapter = new SmartThemeAdapter();
      const userData = defaultAdapter.getUserBehaviorData();

      expect(userData.preferences.preferredTheme).toBe('auto');
      expect(userData.preferences.autoSwitchEnabled).toBe(true);
      expect(userData.preferences.adaptationSensitivity).toBe(0.7);
    });

    it('应该接受自定义配置', () => {
      const customAdapter = new SmartThemeAdapter({
        isLearningEnabled: false,
        adaptationSensitivity: 0.5,
        initialUserBehavior: {
          preferences: {
            preferredTheme: 'dark',
            autoSwitchEnabled: false,
            adaptationSensitivity: 0.5,
            lastManualChange: 0,
          },
        },
      });

      const userData = customAdapter.getUserBehaviorData();
      expect(userData.preferences.preferredTheme).toBe('dark');
      expect(userData.preferences.autoSwitchEnabled).toBe(false);
    });

    it('应该从本地存储加载数据', () => {
      const storedData = {
        preferences: {
          preferredTheme: 'light',
          autoSwitchEnabled: true,
          adaptationSensitivity: 0.8,
          lastManualChange: Date.now(),
        },
      };

      localStorageMock.getItem.mockReturnValue(JSON.stringify(storedData));

      const adapterWithStorage = new SmartThemeAdapter();
      const userData = adapterWithStorage.getUserBehaviorData();

      expect(userData.preferences.preferredTheme).toBe('light');
      expect(userData.preferences.adaptationSensitivity).toBe(0.8);
    });
  });

  describe('主题适配建议生成', () => {
    it('应该生成基本的主题适配建议', async () => {
      const suggestion = await adapter.generateAdaptationSuggestion('light');

      expect(suggestion).toBeDefined();
      expect(suggestion.suggestedTheme).toMatch(/^(light|dark|auto|system)$/);
      expect(suggestion.confidence).toBeGreaterThanOrEqual(0);
      expect(suggestion.confidence).toBeLessThanOrEqual(1);
      expect(suggestion.priority).toBeGreaterThanOrEqual(1);
      expect(suggestion.priority).toBeLessThanOrEqual(10);
      expect(suggestion.reasoning).toBeTruthy();
      expect(suggestion.timestamp).toBeGreaterThan(0);
    });

    it('应该基于时间生成适配建议', async () => {
      // 模拟夜间时间
      const nightTime = new Date();
      nightTime.setHours(23, 0, 0, 0);

      vi.spyOn(Date, 'now').mockReturnValue(nightTime.getTime());
      vi.spyOn(global, 'Date').mockImplementation(() => nightTime);

      const environmentData: Partial<EnvironmentData> = {
        time: {
          current: nightTime,
          timeOfDay: 'night',
          timezone: 'Asia/Shanghai',
        },
      };

      const suggestion = await adapter.generateAdaptationSuggestion(
        'light',
        environmentData
      );

      // 夜间应该倾向于推荐深色主题
      expect(['dark', 'auto']).toContain(suggestion.suggestedTheme);
      expect(suggestion.reason).toMatch(
        /time-based|ambient-light|user-preference/
      );
    });

    it('应该基于环境光线生成适配建议', async () => {
      const environmentData: Partial<EnvironmentData> = {
        light: {
          level: 'very-dark',
          source: 'sensor',
        },
      };

      const suggestion = await adapter.generateAdaptationSuggestion(
        'light',
        environmentData
      );

      // 很暗的环境应该推荐深色主题
      expect(['dark', 'auto']).toContain(suggestion.suggestedTheme);
    });

    it('应该基于无障碍需求生成适配建议', async () => {
      const environmentData: Partial<EnvironmentData> = {
        accessibility: {
          highContrast: true,
          reducedMotion: false,
          largeText: false,
        },
      };

      const suggestion = await adapter.generateAdaptationSuggestion(
        'light',
        environmentData
      );

      // 高对比度需求应该推荐深色主题
      expect(suggestion.suggestedTheme).toBe('dark');
      expect(suggestion.reason).toBe('accessibility');
      expect(suggestion.priority).toBe(10); // 无障碍需求优先级最高
    });
  });

  describe('用户行为学习', () => {
    it('应该记录主题使用行为', () => {
      const theme: SupportedTheme = 'dark';
      const duration = 60000; // 1分钟
      const activity: UserActivity = 'coding';

      adapter.recordThemeUsage(theme, duration, activity);

      const userData = adapter.getUserBehaviorData();
      const usage = userData.themeUsage[theme];

      expect(usage.totalTime).toBe(duration);
      expect(usage.sessionCount).toBe(1);
      expect(usage.activityDistribution[activity]).toBe(duration);
    });

    it('应该累积多次使用记录', () => {
      adapter.recordThemeUsage('dark', 30000, 'coding');
      adapter.recordThemeUsage('dark', 45000, 'reading');

      const userData = adapter.getUserBehaviorData();
      const usage = userData.themeUsage.dark;

      expect(usage.totalTime).toBe(75000);
      expect(usage.sessionCount).toBe(2);
      expect(usage.activityDistribution.coding).toBe(30000);
      expect(usage.activityDistribution.reading).toBe(45000);
    });

    it('应该更新统计信息', () => {
      adapter.recordThemeUsage('light', 60000, 'browsing');
      adapter.recordThemeUsage('dark', 120000, 'coding');

      const userData = adapter.getUserBehaviorData();
      const stats = userData.statistics;

      expect(stats.totalSessions).toBe(2);
      expect(stats.averageSessionDuration).toBe(90000); // (60000 + 120000) / 2
      expect(stats.mostUsedTheme).toBe('dark'); // 使用时间更长
    });
  });

  describe('适配反馈记录', () => {
    it('应该记录适配建议的接受情况', async () => {
      // 生成一个建议
      const suggestion = await adapter.generateAdaptationSuggestion('light');

      // 记录接受反馈
      adapter.recordAdaptationFeedback(suggestion.timestamp.toString(), true);

      const userData = adapter.getUserBehaviorData();
      expect(userData.statistics.adaptationAcceptanceRate).toBeGreaterThan(0);
    });

    it('应该正确计算接受率', async () => {
      // 生成多个建议并记录反馈
      for (let i = 0; i < 5; i++) {
        const suggestion = await adapter.generateAdaptationSuggestion('light');
        adapter.recordAdaptationFeedback(
          suggestion.timestamp.toString(),
          i < 3
        ); // 前3个接受，后2个拒绝
      }

      const userData = adapter.getUserBehaviorData();
      expect(userData.statistics.adaptationAcceptanceRate).toBeCloseTo(0.6, 1); // 3/5 = 0.6
    });
  });

  describe('数据持久化', () => {
    it('应该保存数据到本地存储', () => {
      adapter.recordThemeUsage('dark', 60000, 'coding');

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'smart-theme-adapter-behavior',
        expect.stringContaining('"dark"')
      );
    });

    it('应该处理本地存储错误', () => {
      localStorageMock.setItem.mockImplementation(() => {
        throw new Error('Storage quota exceeded');
      });

      // 应该不抛出错误
      expect(() => {
        adapter.recordThemeUsage('dark', 60000, 'coding');
      }).not.toThrow();
    });
  });

  describe('问题管理', () => {
    it('应该存储和检索适配历史', async () => {
      const suggestion1 = await adapter.generateAdaptationSuggestion('light');
      const suggestion2 = await adapter.generateAdaptationSuggestion('dark');

      const history = adapter.getAdaptationHistory();

      expect(history).toHaveLength(2);
      expect(history[0].timestamp).toBe(suggestion1.timestamp);
      expect(history[1].timestamp).toBe(suggestion2.timestamp);
    });

    it('应该限制历史记录长度', async () => {
      // 生成超过限制的建议
      for (let i = 0; i < 120; i++) {
        await adapter.generateAdaptationSuggestion('light');
      }

      const history = adapter.getAdaptationHistory();
      expect(history.length).toBeLessThanOrEqual(100); // 应该被限制在100条以内
    });
  });

  describe('数据清理', () => {
    it('应该清除所有数据', async () => {
      // 生成一些数据
      adapter.recordThemeUsage('dark', 60000, 'coding');
      await adapter.generateAdaptationSuggestion('light');

      // 清除数据
      adapter.clearAllData();

      const userData = adapter.getUserBehaviorData();
      const history = adapter.getAdaptationHistory();

      expect(userData.statistics.totalSessions).toBe(0);
      expect(history).toHaveLength(0);
      expect(localStorageMock.removeItem).toHaveBeenCalledWith(
        'smart-theme-adapter-behavior'
      );
    });
  });

  describe('边界条件', () => {
    it('应该处理无效的环境数据', async () => {
      const invalidEnvironmentData = {
        light: {
          level: 'invalid-level' as AmbientLightLevel,
          source: 'sensor' as const,
        },
      };

      const suggestion = await adapter.generateAdaptationSuggestion(
        'light',
        invalidEnvironmentData
      );

      expect(suggestion).toBeDefined();
      expect(suggestion.suggestedTheme).toMatch(/^(light|dark|auto|system)$/);
    });

    it('应该处理极端的时间值', async () => {
      const extremeTime = new Date('2099-12-31T23:59:59.999Z');

      const environmentData: Partial<EnvironmentData> = {
        time: {
          current: extremeTime,
          timeOfDay: 'late-night',
          timezone: 'UTC',
        },
      };

      const suggestion = await adapter.generateAdaptationSuggestion(
        'light',
        environmentData
      );

      expect(suggestion).toBeDefined();
      expect(suggestion.timestamp).toBeGreaterThan(0);
    });

    it('应该处理学习功能禁用的情况', () => {
      const nonLearningAdapter = new SmartThemeAdapter({
        isLearningEnabled: false,
      });

      // 记录使用行为不应该更新数据
      const beforeData = nonLearningAdapter.getUserBehaviorData();
      nonLearningAdapter.recordThemeUsage('dark', 60000, 'coding');
      const afterData = nonLearningAdapter.getUserBehaviorData();

      expect(beforeData.statistics.totalSessions).toBe(
        afterData.statistics.totalSessions
      );
    });
  });

  describe('时间段检测', () => {
    it('应该正确检测不同时间段', () => {
      const testCases: Array<[number, TimeOfDay]> = [
        [6, 'dawn'],
        [9, 'morning'],
        [14, 'afternoon'],
        [18, 'evening'],
        [21, 'night'],
        [2, 'late-night'],
      ];

      testCases.forEach(([hour, expectedTimeOfDay]) => {
        const testDate = new Date();
        testDate.setHours(hour, 0, 0, 0);

        vi.spyOn(global, 'Date').mockImplementation(() => testDate);

        // 简化测试，只检查时间段检测的基本功能
        const currentHour = testDate.getHours();
        let expectedResult: TimeOfDay;

        if (currentHour >= 5 && currentHour < 7) expectedResult = 'dawn';
        else if (currentHour >= 7 && currentHour < 12)
          expectedResult = 'morning';
        else if (currentHour >= 12 && currentHour < 17)
          expectedResult = 'afternoon';
        else if (currentHour >= 17 && currentHour < 20)
          expectedResult = 'evening';
        else if (currentHour >= 20 && currentHour < 23)
          expectedResult = 'night';
        else expectedResult = 'late-night';

        expect(expectedResult).toBe(expectedTimeOfDay);
      });
    });
  });

  describe('环境光线估算', () => {
    it('应该基于时间段估算光线级别', async () => {
      const testCases: Array<[TimeOfDay, AmbientLightLevel]> = [
        ['dawn', 'dim'],
        ['morning', 'bright'],
        ['afternoon', 'very-bright'],
        ['evening', 'normal'],
        ['night', 'dim'],
        ['late-night', 'dark'],
      ];

      for (const [timeOfDay, expectedLight] of testCases) {
        const environmentData: Partial<EnvironmentData> = {
          time: {
            current: new Date(),
            timeOfDay,
            timezone: 'UTC',
          },
        };

        const suggestion = await adapter.generateAdaptationSuggestion(
          'light',
          environmentData
        );

        // 检查建议是否生成成功
        expect(suggestion).toBeDefined();
        expect(suggestion.suggestedTheme).toMatch(/^(light|dark|auto|system)$/);

        // 检查是否包含时间段信息（可能在不同的建议中）
        if (suggestion.metadata.timeOfDay) {
          expect(suggestion.metadata.timeOfDay).toBe(timeOfDay);
        }
      }
    });
  });
});
