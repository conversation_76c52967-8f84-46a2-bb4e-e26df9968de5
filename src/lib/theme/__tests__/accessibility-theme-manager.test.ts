/**
 * 无障碍主题管理器测试
 * 
 * 测试覆盖：
 * - 管理器初始化
 * - 配置管理
 * - 系统偏好检测
 * - 事件处理
 * - 样式应用
 * - 本地存储
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

import { AccessibilityThemeManager } from '../accessibility-theme-manager';

// Mock DOM APIs
const mockMatchMedia = vi.fn();
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
};

const mockDocumentElement = {
  classList: {
    add: vi.fn(),
    remove: vi.fn(),
    contains: vi.fn(),
  },
  style: {
    setProperty: vi.fn(),
    removeProperty: vi.fn(),
  },
  setAttribute: vi.fn(),
  removeAttribute: vi.fn(),
};

const mockDocument = {
  documentElement: mockDocumentElement,
  createElement: vi.fn(() => ({
    style: { cssText: '' },
  })),
  body: {
    appendChild: vi.fn(),
    removeChild: vi.fn(),
  },
};

// Mock window APIs
Object.defineProperty(global, 'window', {
  value: {
    matchMedia: mockMatchMedia,
    getComputedStyle: vi.fn(() => ({
      fontSize: '16px',
    })),
    performance: {
      now: vi.fn(() => Date.now()),
    },
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
  },
  writable: true,
});

Object.defineProperty(global, 'document', {
  value: mockDocument,
  writable: true,
});

Object.defineProperty(global, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
});

Object.defineProperty(global, 'navigator', {
  value: {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  },
  writable: true,
});

describe('AccessibilityThemeManager', () => {
  let manager: AccessibilityThemeManager;
  let mockMediaQueryList: any;

  beforeEach(() => {
    // 重置所有 mock
    vi.clearAllMocks();
    
    // 设置 matchMedia mock
    mockMediaQueryList = {
      matches: false,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
    };
    mockMatchMedia.mockReturnValue(mockMediaQueryList);
    
    // 重置 localStorage mock
    mockLocalStorage.getItem.mockReturnValue(null);
    
    // 重置 document mock
    mockDocument.createElement.mockReturnValue({
      style: { cssText: '' },
    });
  });

  afterEach(() => {
    if (manager) {
      manager.destroy();
    }
  });

  describe('初始化', () => {
    it('应该能够创建管理器实例', () => {
      manager = new AccessibilityThemeManager();
      expect(manager).toBeInstanceOf(AccessibilityThemeManager);
    });

    it('应该能够使用初始配置创建实例', () => {
      const initialConfig = {
        highContrast: true,
        fontSize: 1.5,
      };
      
      manager = new AccessibilityThemeManager(initialConfig);
      const config = manager.getConfig();
      
      expect(config.highContrast).toBe(true);
      expect(config.fontSize).toBe(1.5);
    });

    it('应该检测系统偏好', () => {
      // 模拟减少动画偏好
      mockMediaQueryList.matches = true;
      mockMatchMedia.mockImplementation((query) => {
        if (query.includes('prefers-reduced-motion')) {
          return { ...mockMediaQueryList, matches: true };
        }
        return mockMediaQueryList;
      });

      manager = new AccessibilityThemeManager();
      const systemPrefs = manager.getSystemPreferences();
      
      expect(systemPrefs.prefersReducedMotion).toBe(true);
    });

    it('应该设置媒体查询监听器', () => {
      manager = new AccessibilityThemeManager();
      
      // 验证 matchMedia 被调用
      expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-reduced-motion: reduce)');
      expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-contrast: high)');
      expect(mockMatchMedia).toHaveBeenCalledWith('(prefers-color-scheme: dark)');
    });
  });

  describe('配置管理', () => {
    beforeEach(() => {
      manager = new AccessibilityThemeManager();
    });

    it('应该能够获取当前配置', () => {
      const config = manager.getConfig();
      
      expect(config).toHaveProperty('highContrast');
      expect(config).toHaveProperty('reducedMotion');
      expect(config).toHaveProperty('largeText');
      expect(config).toHaveProperty('fontSize');
      expect(config).toHaveProperty('contrastLevel');
      expect(config).toHaveProperty('colorBlindType');
    });

    it('应该能够更新配置', () => {
      const updates = {
        highContrast: true,
        fontSize: 1.25,
      };
      
      manager.updateConfig(updates);
      const config = manager.getConfig();
      
      expect(config.highContrast).toBe(true);
      expect(config.fontSize).toBe(1.25);
    });

    it('应该能够切换高对比度模式', () => {
      const initialConfig = manager.getConfig();
      const initialHighContrast = initialConfig.highContrast;
      
      manager.toggleHighContrast();
      const updatedConfig = manager.getConfig();
      
      expect(updatedConfig.highContrast).toBe(!initialHighContrast);
    });

    it('应该能够切换减少动画模式', () => {
      manager.toggleReducedMotion();
      const config = manager.getConfig();
      
      expect(config.reducedMotion).toBe(true);
      expect(config.animationDuration).toBe(0);
    });

    it('应该能够设置字体缩放', () => {
      manager.setFontScale(1.5);
      const config = manager.getConfig();
      
      expect(config.fontSize).toBe(1.5);
    });

    it('应该限制字体缩放范围', () => {
      manager.setFontScale(5.0); // 超出最大值
      expect(manager.getConfig().fontSize).toBe(3.0);
      
      manager.setFontScale(0.1); // 低于最小值
      expect(manager.getConfig().fontSize).toBe(0.5);
    });

    it('应该能够设置对比度级别', () => {
      manager.setContrastLevel('high');
      const config = manager.getConfig();
      
      expect(config.contrastLevel).toBe('high');
      expect(config.highContrast).toBe(true);
    });

    it('应该能够设置色盲友好模式', () => {
      manager.setColorBlindMode('protanopia');
      const config = manager.getConfig();
      
      expect(config.colorBlindType).toBe('protanopia');
      expect(config.colorBlindFriendly).toBe(true);
    });

    it('应该能够重置为默认设置', () => {
      // 先修改一些设置
      manager.updateConfig({
        highContrast: true,
        fontSize: 2.0,
        reducedMotion: true,
      });
      
      // 重置为默认
      manager.resetToDefaults();
      const config = manager.getConfig();
      
      expect(config.highContrast).toBe(false);
      expect(config.fontSize).toBe(1.0);
      expect(config.reducedMotion).toBe(false);
    });
  });

  describe('系统偏好处理', () => {
    beforeEach(() => {
      manager = new AccessibilityThemeManager();
    });

    it('应该能够应用系统推荐设置', () => {
      // 模拟系统偏好
      manager['systemPreferences'] = {
        prefersReducedMotion: true,
        prefersHighContrast: true,
        prefersColorScheme: 'dark',
        fontSize: 1.5,
        forcedColors: false,
      };
      
      manager.applySystemRecommendations();
      const config = manager.getConfig();
      
      expect(config.reducedMotion).toBe(true);
      expect(config.highContrast).toBe(true);
      expect(config.largeText).toBe(true);
      expect(config.fontSize).toBe(1.5);
    });

    it('应该处理强制颜色模式', () => {
      manager['systemPreferences'] = {
        prefersReducedMotion: false,
        prefersHighContrast: false,
        prefersColorScheme: 'light',
        fontSize: 1.0,
        forcedColors: true,
      };
      
      manager.applySystemRecommendations();
      const config = manager.getConfig();
      
      expect(config.highContrast).toBe(true);
      expect(config.contrastLevel).toBe('maximum');
    });
  });

  describe('事件处理', () => {
    beforeEach(() => {
      manager = new AccessibilityThemeManager();
    });

    it('应该发出配置变化事件', (done) => {
      manager.addEventListener('accessibility-change', (event: any) => {
        const { type, config } = event.detail;
        expect(type).toBe('config-changed');
        expect(config).toBeDefined();
        done();
      });
      
      manager.updateConfig({ highContrast: true });
    });

    it('应该发出高对比度切换事件', (done) => {
      manager.addEventListener('accessibility-change', (event: any) => {
        const { type } = event.detail;
        if (type === 'high-contrast-toggled') {
          done();
        }
      });
      
      manager.toggleHighContrast();
    });

    it('应该发出字体大小变化事件', (done) => {
      manager.addEventListener('accessibility-change', (event: any) => {
        const { type } = event.detail;
        if (type === 'font-size-changed') {
          done();
        }
      });
      
      manager.setFontScale(1.5);
    });
  });

  describe('样式应用', () => {
    beforeEach(() => {
      manager = new AccessibilityThemeManager();
    });

    it('应该应用高对比度样式', () => {
      manager.updateConfig({ highContrast: true, contrastLevel: 'high' });
      
      expect(mockDocumentElement.classList.add).toHaveBeenCalledWith('accessibility-high-contrast');
      expect(mockDocumentElement.setAttribute).toHaveBeenCalledWith('data-contrast-level', 'high');
    });

    it('应该应用减少动画样式', () => {
      manager.updateConfig({ reducedMotion: true, animationDuration: 0 });
      
      expect(mockDocumentElement.classList.add).toHaveBeenCalledWith('accessibility-reduced-motion');
      expect(mockDocumentElement.style.setProperty).toHaveBeenCalledWith('--animation-duration-multiplier', '0');
    });

    it('应该应用字体缩放', () => {
      manager.updateConfig({ fontSize: 1.5 });
      
      expect(mockDocumentElement.style.setProperty).toHaveBeenCalledWith('--accessibility-font-scale', '1.5');
    });

    it('应该移除样式类当禁用功能时', () => {
      // 先启用
      manager.updateConfig({ highContrast: true });
      
      // 再禁用
      manager.updateConfig({ highContrast: false });
      
      expect(mockDocumentElement.classList.remove).toHaveBeenCalledWith('accessibility-high-contrast');
    });
  });

  describe('本地存储', () => {
    beforeEach(() => {
      manager = new AccessibilityThemeManager();
    });

    it('应该保存配置到本地存储', () => {
      manager.updateConfig({ highContrast: true });
      
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'accessibility-config',
        expect.stringContaining('"highContrast":true')
      );
    });

    it('应该从本地存储加载配置', () => {
      const storedConfig = {
        highContrast: true,
        fontSize: 1.5,
      };
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(storedConfig));
      
      manager = new AccessibilityThemeManager();
      const config = manager.getConfig();
      
      expect(config.highContrast).toBe(true);
      expect(config.fontSize).toBe(1.5);
    });

    it('应该处理无效的存储数据', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid json');
      
      // 不应该抛出错误
      expect(() => {
        manager = new AccessibilityThemeManager();
      }).not.toThrow();
    });
  });

  describe('屏幕阅读器检测', () => {
    it('应该检测屏幕阅读器', () => {
      // 模拟屏幕阅读器用户代理
      Object.defineProperty(global, 'navigator', {
        value: {
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) NVDA/2021.1',
        },
        writable: true,
      });
      
      manager = new AccessibilityThemeManager();
      const config = manager.getConfig();
      
      expect(config.screenReaderOptimized).toBe(true);
      expect(config.focusIndicatorEnhanced).toBe(true);
    });

    it('应该检测语音合成 API', () => {
      Object.defineProperty(global, 'window', {
        value: {
          ...global.window,
          speechSynthesis: {},
        },
        writable: true,
      });
      
      manager = new AccessibilityThemeManager();
      const config = manager.getConfig();
      
      expect(config.screenReaderOptimized).toBe(true);
    });
  });

  describe('销毁', () => {
    it('应该能够销毁管理器', () => {
      manager = new AccessibilityThemeManager();
      
      expect(() => {
        manager.destroy();
      }).not.toThrow();
    });

    it('应该移除事件监听器', () => {
      manager = new AccessibilityThemeManager();
      manager.destroy();
      
      expect(mockMediaQueryList.removeEventListener).toHaveBeenCalled();
    });
  });
});
