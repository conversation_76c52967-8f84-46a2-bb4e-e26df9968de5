/**
 * 动态翻译加载系统
 *
 * 功能特性：
 * - 按需加载翻译文件，减少初始加载时间
 * - 翻译文件分割和模块化管理
 * - 智能缓存策略和内存管理
 * - 加载状态管理和错误处理
 * - 预加载和懒加载策略
 * - 企业级类型安全和性能优化
 */
import type { SupportedLocale } from './language-detection';

// 翻译模块类型定义
export type TranslationModule =
  | 'common' // 通用翻译（按钮、标签等）
  | 'navigation' // 导航相关
  | 'home' // 首页内容
  | 'about' // 关于页面
  | 'contact' // 联系页面
  | 'blog' // 博客相关
  | 'products' // 产品相关
  | 'services' // 服务相关
  | 'forms' // 表单相关
  | 'errors'; // 错误信息

// 翻译数据类型
export type TranslationData = Record<string, unknown>;

// 加载状态类型
export type LoadingState = 'idle' | 'loading' | 'loaded' | 'error';

// 翻译模块信息
export interface TranslationModuleInfo {
  module: TranslationModule;
  locale: SupportedLocale;
  priority: number; // 加载优先级 (1-10, 10最高)
  size: number; // 预估文件大小（字节）
  dependencies?: TranslationModule[]; // 依赖的其他模块
}

// 加载结果接口
export interface LoadResult {
  success: boolean;
  data?: TranslationData;
  error?: Error;
  fromCache: boolean;
  loadTime: number;
}

// 缓存条目接口
interface CacheEntry {
  data: TranslationData;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
  size: number;
}

// 加载统计接口
export interface LoadingStats {
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  totalLoadTime: number;
  averageLoadTime: number;
  errorCount: number;
  moduleStats: Record<
    TranslationModule,
    {
      requests: number;
      hits: number;
      errors: number;
      totalSize: number;
    }
  >;
}

// 翻译模块配置
const MODULE_CONFIG: Record<TranslationModule, TranslationModuleInfo> = {
  common: {
    module: 'common',
    locale: 'en', // 会被动态设置
    priority: 10, // 最高优先级
    size: 2048, // 2KB
  },
  navigation: {
    module: 'navigation',
    locale: 'en',
    priority: 9,
    size: 1024, // 1KB
    dependencies: ['common'],
  },
  home: {
    module: 'home',
    locale: 'en',
    priority: 8,
    size: 8192, // 8KB
    dependencies: ['common', 'navigation'],
  },
  about: {
    module: 'about',
    locale: 'en',
    priority: 5,
    size: 4096, // 4KB
    dependencies: ['common', 'navigation'],
  },
  contact: {
    module: 'contact',
    locale: 'en',
    priority: 6,
    size: 3072, // 3KB
    dependencies: ['common', 'navigation', 'forms'],
  },
  blog: {
    module: 'blog',
    locale: 'en',
    priority: 4,
    size: 6144, // 6KB
    dependencies: ['common', 'navigation'],
  },
  products: {
    module: 'products',
    locale: 'en',
    priority: 7,
    size: 10240, // 10KB
    dependencies: ['common', 'navigation'],
  },
  services: {
    module: 'services',
    locale: 'en',
    priority: 5,
    size: 5120, // 5KB
    dependencies: ['common', 'navigation'],
  },
  forms: {
    module: 'forms',
    locale: 'en',
    priority: 6,
    size: 4096, // 4KB
    dependencies: ['common'],
  },
  errors: {
    module: 'errors',
    locale: 'en',
    priority: 8,
    size: 2048, // 2KB
    dependencies: ['common'],
  },
};

/**
 * 动态翻译加载器类
 */
export class DynamicTranslationLoader {
  private readonly cache = new Map<string, CacheEntry>();
  private readonly loadingStates = new Map<string, LoadingState>();
  private readonly loadingPromises = new Map<string, Promise<LoadResult>>();
  private readonly stats: LoadingStats;

  // 配置选项
  private readonly maxCacheSize: number;
  private readonly cacheTimeout: number;
  private readonly maxRetries: number;
  private readonly retryDelay: number;

  constructor(
    options: {
      maxCacheSize?: number; // 最大缓存大小（字节）
      cacheTimeout?: number; // 缓存超时时间（毫秒）
      maxRetries?: number; // 最大重试次数
      retryDelay?: number; // 重试延迟（毫秒）
    } = {}
  ) {
    this.maxCacheSize = options.maxCacheSize ?? 10 * 1024 * 1024; // 10MB
    this.cacheTimeout = options.cacheTimeout ?? 30 * 60 * 1000; // 30分钟
    this.maxRetries = options.maxRetries ?? 3;
    this.retryDelay = options.retryDelay ?? 1000;

    // 初始化统计信息
    this.stats = {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      totalLoadTime: 0,
      averageLoadTime: 0,
      errorCount: 0,
      moduleStats: {} as Record<TranslationModule, any>,
    };

    // 初始化模块统计
    Object.keys(MODULE_CONFIG).forEach((module) => {
      this.stats.moduleStats[module as TranslationModule] = {
        requests: 0,
        hits: 0,
        errors: 0,
        totalSize: 0,
      };
    });
  }

  /**
   * 生成缓存键
   */
  private getCacheKey(
    module: TranslationModule,
    locale: SupportedLocale
  ): string {
    return `${locale}:${module}`;
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(entry: CacheEntry): boolean {
    const now = Date.now();
    return now - entry.timestamp < this.cacheTimeout;
  }

  /**
   * 清理过期缓存
   */
  private cleanupCache(): void {
    const now = Date.now();
    const entries = [...this.cache.entries()];

    // 按最后访问时间排序
    entries.sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

    let totalSize = 0;
    const validEntries: Array<[string, CacheEntry]> = [];

    // 移除过期条目，计算总大小
    for (const [key, entry] of entries) {
      if (this.isCacheValid(entry)) {
        validEntries.push([key, entry]);
        totalSize += entry.size;
      } else {
        this.cache.delete(key);
      }
    }

    // 如果超过最大缓存大小，移除最少使用的条目
    while (totalSize > this.maxCacheSize && validEntries.length > 0) {
      const [key, entry] = validEntries.shift()!;
      this.cache.delete(key);
      totalSize -= entry.size;
    }
  }

  /**
   * 从缓存获取翻译数据
   */
  private getFromCache(
    module: TranslationModule,
    locale: SupportedLocale
  ): TranslationData | null {
    const key = this.getCacheKey(module, locale);
    const entry = this.cache.get(key);

    if (!entry || !this.isCacheValid(entry)) {
      return null;
    }

    // 更新访问统计
    entry.accessCount++;
    entry.lastAccessed = Date.now();

    return entry.data;
  }

  /**
   * 将数据存储到缓存
   */
  private setCache(
    module: TranslationModule,
    locale: SupportedLocale,
    data: TranslationData
  ): void {
    const key = this.getCacheKey(module, locale);
    const size = JSON.stringify(data).length * 2; // 估算内存大小

    const entry: CacheEntry = {
      data,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccessed: Date.now(),
      size,
    };

    this.cache.set(key, entry);

    // 清理缓存
    this.cleanupCache();
  }

  /**
   * 实际加载翻译文件
   */
  private async loadTranslationFile(
    module: TranslationModule,
    locale: SupportedLocale,
    retryCount = 0
  ): Promise<TranslationData> {
    try {
      // 动态导入翻译文件
      const response = await fetch(`/api/translations/${locale}/${module}`);

      if (!response.ok) {
        throw new Error(
          `Failed to load translation: ${response.status} ${response.statusText}`
        );
      }

      return await response.json();
    } catch (error) {
      // 重试逻辑
      if (retryCount < this.maxRetries) {
        await new Promise((resolve) =>
          setTimeout(resolve, this.retryDelay * (retryCount + 1))
        );
        return this.loadTranslationFile(module, locale, retryCount + 1);
      }

      throw error;
    }
  }

  /**
   * 加载翻译模块
   */
  async loadModule(
    module: TranslationModule,
    locale: SupportedLocale
  ): Promise<LoadResult> {
    const startTime = Date.now();
    const key = this.getCacheKey(module, locale);

    // 更新统计
    this.stats.totalRequests++;
    this.stats.moduleStats[module].requests++;

    // 检查缓存
    const cachedData = this.getFromCache(module, locale);
    if (cachedData) {
      this.stats.cacheHits++;
      this.stats.moduleStats[module].hits++;

      return {
        success: true,
        data: cachedData,
        fromCache: true,
        loadTime: Date.now() - startTime,
      };
    }

    this.stats.cacheMisses++;

    // 检查是否正在加载
    const existingPromise = this.loadingPromises.get(key);
    if (existingPromise) {
      return existingPromise;
    }

    // 设置加载状态
    this.loadingStates.set(key, 'loading');

    // 创建加载 Promise
    const loadingPromise = (async (): Promise<LoadResult> => {
      try {
        const data = await this.loadTranslationFile(module, locale);

        // 存储到缓存
        this.setCache(module, locale, data);

        // 更新状态
        this.loadingStates.set(key, 'loaded');

        const loadTime = Date.now() - startTime;
        this.stats.totalLoadTime += loadTime;
        this.stats.averageLoadTime =
          this.stats.totalLoadTime / this.stats.totalRequests;
        this.stats.moduleStats[module].totalSize += JSON.stringify(data).length;

        return {
          success: true,
          data,
          fromCache: false,
          loadTime,
        };
      } catch (error) {
        // 更新错误统计
        this.stats.errorCount++;
        this.stats.moduleStats[module].errors++;

        // 设置错误状态
        this.loadingStates.set(key, 'error');

        return {
          success: false,
          error: error as Error,
          fromCache: false,
          loadTime: Date.now() - startTime,
        };
      } finally {
        // 清理加载 Promise
        this.loadingPromises.delete(key);
      }
    })();

    // 存储加载 Promise
    this.loadingPromises.set(key, loadingPromise);

    return loadingPromise;
  }

  /**
   * 批量加载多个模块
   */
  async loadModules(
    modules: TranslationModule[],
    locale: SupportedLocale
  ): Promise<Record<TranslationModule, LoadResult>> {
    const results = await Promise.allSettled(
      modules.map((module) => this.loadModule(module, locale))
    );

    const output: Record<TranslationModule, LoadResult> = {} as Record<
      TranslationModule,
      LoadResult
    >;

    modules.forEach((module, index) => {
      const result = results[index];
      if (result !== null && result !== undefined && result.status === 'fulfilled') {
        output[module] = result.value;
      } else if (result !== null && result !== undefined && result.status === 'rejected') {
        output[module] = {
          success: false,
          error: new Error(result.reason),
          fromCache: false,
          loadTime: 0,
        };
      } else {
        // 处理 undefined 的情况
        output[module] = {
          success: false,
          error: new Error('Unknown error occurred'),
          fromCache: false,
          loadTime: 0,
        };
      }
    });

    return output;
  }

  /**
   * 预加载模块（按优先级）
   */
  async preloadModules(
    locale: SupportedLocale,
    maxConcurrent = 3
  ): Promise<void> {
    const modules = Object.keys(MODULE_CONFIG) as TranslationModule[];

    // 按优先级排序
    modules.sort(
      (a, b) => MODULE_CONFIG[b].priority - MODULE_CONFIG[a].priority
    );

    // 分批加载
    for (let i = 0; i < modules.length; i += maxConcurrent) {
      const batch = modules.slice(i, i + maxConcurrent);
      await Promise.allSettled(
        batch.map((module) => this.loadModule(module, locale))
      );
    }
  }

  /**
   * 获取模块加载状态
   */
  getLoadingState(
    module: TranslationModule,
    locale: SupportedLocale
  ): LoadingState {
    const key = this.getCacheKey(module, locale);
    return this.loadingStates.get(key) || 'idle';
  }

  /**
   * 获取加载统计信息
   */
  getStats(): LoadingStats {
    return { ...this.stats };
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear();
    this.loadingStates.clear();
    this.loadingPromises.clear();
  }

  /**
   * 获取缓存信息
   */
  getCacheInfo(): {
    size: number;
    entries: number;
    totalMemory: number;
  } {
    let totalMemory = 0;
    for (const entry of this.cache.values()) {
      totalMemory += entry.size;
    }

    return {
      size: this.cache.size,
      entries: this.cache.size,
      totalMemory,
    };
  }
}

// 全局实例
export const translationLoader = new DynamicTranslationLoader({
  maxCacheSize: 10 * 1024 * 1024, // 10MB
  cacheTimeout: 30 * 60 * 1000, // 30分钟
  maxRetries: 3,
  retryDelay: 1000,
});
