import type { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';

import { UnderConstruction } from '@/components/content/under-construction';

/**
 * 页面组件属性接口
 */
interface PageProps {
  params: Promise<{
    locale: string;
  }>;
}

/**
 * 创建建设中页面的工厂函数
 *
 * 用于消除 about、blog、products 页面的代码重复
 * 提供统一的页面结构和 metadata 生成逻辑
 *
 * @param namespace - 翻译命名空间，如 'pages.about'
 * @returns 包含 generateMetadata 和 PageComponent 的对象
 */
export function createUnderConstructionPage(namespace: string): {
  generateMetadata: ({ params }: PageProps) => Promise<Metadata>;
  PageComponent: ({ params }: PageProps) => React.JSX.Element;
} {
  /**
   * 生成页面 metadata
   */
  const generateMetadata = async ({ params }: PageProps): Promise<Metadata> => {
    const { locale } = await params;
    const t = await getTranslations({ locale, namespace });

    return {
      title: t('title'),
      description: t('description'),
      openGraph: {
        title: t('title'),
        description: t('description'),
        type: 'website',
        locale,
      },
      twitter: {
        card: 'summary_large_image',
        title: t('title'),
        description: t('description'),
      },
      robots: {
        index: true,
        follow: true,
      },
    };
  };

  /**
   * 页面组件
   */
  const PageComponent = (): React.JSX.Element => {
    return <UnderConstruction />;
  };

  return { generateMetadata, PageComponent };
}
